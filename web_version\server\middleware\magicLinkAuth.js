/**
 * Magic Link Authentication Middleware
 * 
 * Handles JWT session token verification for Magic Link authentication system.
 */

const jwt = require('jsonwebtoken');
const MagicLinkUser = require('../models/MagicLinkUser');
const magicLinkService = require('../services/magicLinkService');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errorHandler');

/**
 * Protect routes - require valid session token
 */
const protect = async (req, res, next) => {
  try {
    let token;

    // Get token from Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    // Get token from cookies (if using cookie-based auth)
    else if (req.cookies && req.cookies.sessionToken) {
      token = req.cookies.sessionToken;
    }

    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: 'Access denied. No authentication token provided.'
      });
    }

    // Verify session token
    const decoded = magicLinkService.constructor.verifySessionToken(token);
    
    if (!decoded.valid) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid or expired authentication token.'
      });
    }

    // Get current user from database
    const user = await MagicLinkUser.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        status: 'error',
        message: 'User no longer exists.'
      });
    }

    // Check if account is locked
    if (user.isAccountLocked()) {
      return res.status(423).json({
        status: 'error',
        message: 'Account is temporarily locked. Please try again later.'
      });
    }

    // Attach user to request object
    req.user = {
      userId: user.id,
      email: user.email,
      displayName: user.displayName,
      isVerified: user.isVerified
    };

    next();

  } catch (error) {
    logger.error('Authentication middleware error:', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    return res.status(401).json({
      status: 'error',
      message: 'Authentication failed.'
    });
  }
};

/**
 * Optional authentication - attach user if token is valid, but don't require it
 */
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    // Get token from Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    // Get token from cookies (if using cookie-based auth)
    else if (req.cookies && req.cookies.sessionToken) {
      token = req.cookies.sessionToken;
    }

    if (!token) {
      // No token provided, continue without authentication
      return next();
    }

    // Verify session token
    const decoded = magicLinkService.constructor.verifySessionToken(token);
    
    if (!decoded.valid) {
      // Invalid token, continue without authentication
      return next();
    }

    // Get current user from database
    const user = await MagicLinkUser.findById(decoded.userId);
    
    if (!user || user.isAccountLocked()) {
      // User not found or locked, continue without authentication
      return next();
    }

    // Attach user to request object
    req.user = {
      userId: user.id,
      email: user.email,
      displayName: user.displayName,
      isVerified: user.isVerified
    };

    next();

  } catch (error) {
    logger.error('Optional authentication middleware error:', {
      error: error.message,
      ip: req.ip
    });

    // Continue without authentication on error
    next();
  }
};

/**
 * Require verified user - user must be authenticated and verified
 */
const requireVerified = async (req, res, next) => {
  // First run protect middleware
  protect(req, res, (err) => {
    if (err) return next(err);

    // Check if user is verified
    if (!req.user.isVerified) {
      return res.status(403).json({
        status: 'error',
        message: 'Email verification required. Please check your email and complete verification.'
      });
    }

    next();
  });
};

/**
 * Admin only access (if admin roles are implemented later)
 */
const adminOnly = async (req, res, next) => {
  // First run protect middleware
  protect(req, res, (err) => {
    if (err) return next(err);

    // For now, all verified users have admin access
    // This can be extended with role-based access control later
    if (!req.user.isVerified) {
      return res.status(403).json({
        status: 'error',
        message: 'Admin access required.'
      });
    }

    next();
  });
};

/**
 * Rate limiting by user ID
 */
const createUserRateLimit = (windowMs, max, message) => {
  const attempts = new Map();

  return (req, res, next) => {
    if (!req.user) {
      return next();
    }

    const userId = req.user.userId;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old attempts
    if (attempts.has(userId)) {
      const userAttempts = attempts.get(userId).filter(time => time > windowStart);
      attempts.set(userId, userAttempts);
    }

    // Get current attempts
    const currentAttempts = attempts.get(userId) || [];

    if (currentAttempts.length >= max) {
      return res.status(429).json({
        status: 'error',
        message: message || 'Too many requests. Please try again later.'
      });
    }

    // Record this attempt
    currentAttempts.push(now);
    attempts.set(userId, currentAttempts);

    next();
  };
};

module.exports = {
  protect,
  optionalAuth,
  requireVerified,
  adminOnly,
  createUserRateLimit
};
