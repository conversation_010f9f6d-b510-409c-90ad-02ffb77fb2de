<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT - Offline Steganography Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a192f 0%, #1e3a8a 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4472C4;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.8;
            font-style: italic;
        }
        
        .description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .launch-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .launch-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .launch-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .launch-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #4472C4;
        }
        
        .launch-card p {
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        .btn {
            background: linear-gradient(45deg, #4472C4, #6366f1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(68, 114, 196, 0.4);
        }
        
        .status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(34, 197, 94, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .offline-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(34, 197, 94, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature h4 {
            color: #4472C4;
            margin-bottom: 8px;
        }
        
        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="offline-indicator">🔒 Offline Mode</div>
    
    <div class="container">
        <div class="logo">🛡️ LESAVOT</div>
        <div class="subtitle">THE MORE YOU LOOK, THE LESS YOU SEE</div>
        
        <div class="description">
            Welcome to LESAVOT - Advanced Multimodal Steganographic Security Platform. 
            This offline application provides secure steganography capabilities for text, images, and audio files 
            without requiring an internet connection.
        </div>
        
        <div class="launch-options">
            <div class="launch-card" onclick="openWebApp()">
                <h3>🌐 Web Browser</h3>
                <p>Launch LESAVOT in your web browser with full functionality and modern interface.</p>
                <button class="btn">Launch Web App</button>
            </div>
            
            <div class="launch-card" onclick="openDesktopApp()">
                <h3>🖥️ Desktop Application</h3>
                <p>Run LESAVOT as a native desktop application with enhanced security and offline capabilities.</p>
                <button class="btn">Launch Desktop App</button>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <h4>🔐 Text Steganography</h4>
                <p>Hide messages in plain text using advanced encoding techniques</p>
            </div>
            <div class="feature">
                <h4>🖼️ Image Steganography</h4>
                <p>Embed secret data within image files without visible changes</p>
            </div>
            <div class="feature">
                <h4>🎵 Audio Steganography</h4>
                <p>Conceal information in audio files while preserving quality</p>
            </div>
            <div class="feature">
                <h4>🔒 Military-Grade Security</h4>
                <p>Advanced encryption and password protection for all operations</p>
            </div>
        </div>
        
        <div class="status">
            <strong>✅ System Status:</strong> All steganography modules loaded and ready for offline operation.
            <br><strong>🔒 Security Level:</strong> Maximum - All operations performed locally on your device.
        </div>
    </div>
    
    <script>
        function openWebApp() {
            // Try to open the local web version
            const webAppUrl = './web_version/index.html';
            
            // Check if we can access the local file
            fetch(webAppUrl)
                .then(response => {
                    if (response.ok) {
                        window.open(webAppUrl, '_blank');
                    } else {
                        // Fallback to the current HTTP server if running
                        window.open('http://localhost:3000', '_blank');
                    }
                })
                .catch(() => {
                    // If local file access fails, try localhost
                    window.open('http://localhost:3000', '_blank');
                });
        }
        
        function openDesktopApp() {
            // Check if Electron is available
            if (window.require) {
                // We're in Electron
                alert('You are already running the desktop application!');
                return;
            }
            
            // Try to launch the desktop app
            try {
                // For Windows
                if (navigator.platform.indexOf('Win') > -1) {
                    // Try to run the batch file
                    const link = document.createElement('a');
                    link.href = './desktop_app/run_lesavot_desktop.bat';
                    link.download = 'run_lesavot_desktop.bat';
                    link.click();
                    
                    alert('Desktop launcher downloaded! Run the .bat file to start the desktop application.');
                } else {
                    // For other platforms
                    alert('Desktop application requires Node.js and Electron. Please install dependencies and run: npm start in the desktop_app folder.');
                }
            } catch (error) {
                alert('To run the desktop application:\n1. Install Node.js\n2. Navigate to desktop_app folder\n3. Run: npm install\n4. Run: npm start');
            }
        }
        
        // Check if running in Electron
        if (window.require) {
            document.querySelector('.offline-indicator').textContent = '🖥️ Desktop App';
            document.querySelector('.launch-card:last-child').style.display = 'none';
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', () => {
            // Animate the logo
            const logo = document.querySelector('.logo');
            logo.style.animation = 'pulse 2s infinite';
            
            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                
                .launch-card {
                    animation: fadeIn 0.6s ease-out;
                }
                
                .launch-card:nth-child(2) {
                    animation-delay: 0.2s;
                }
            `;
            document.head.appendChild(style);
        });
        
        // Show current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.title = `LESAVOT - ${timeString}`;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
