# 🚀 LESAVOT Deployment Instructions

## 📋 Overview

This guide explains how to deploy the LESAVOT project to the specified GitHub repositories:

- **Frontend**: `https://github.com/Bechi-cyber/LASAVOT`
- **Backend**: `https://github.com/Bechi-cyber/LASAVOT-Backend`

## 🔧 Automated Deployment (Recommended)

### Option 1: Run the Batch Script
```bash
deploy-simple.bat
```

### Option 2: Run PowerShell Scripts Individually
```powershell
# Deploy Frontend
powershell -ExecutionPolicy Bypass -File "deploy-frontend.ps1"

# Deploy Backend
powershell -ExecutionPolicy Bypass -File "deploy-backend.ps1"
```

## 📝 Manual Deployment Instructions

If the automated scripts don't work, follow these manual steps:

### 🎨 Frontend Deployment

1. **Create/Clone Frontend Repository**
   ```bash
   git clone https://github.com/Bechi-cyber/LASAVOT.git frontend-repo
   cd frontend-repo
   ```

2. **Copy Frontend Files**
   - Copy all files from `web_version/` directory to the repository root
   - Include: `auth.html`, `text_stego.html`, `image_stego.html`, `audio_stego.html`, etc.

3. **Commit and Push**
   ```bash
   git add .
   git commit -m "Frontend deployment - Authentication system updates"
   git push origin main
   ```

### ⚙️ Backend Deployment

1. **Create/Clone Backend Repository**
   ```bash
   git clone https://github.com/Bechi-cyber/LASAVOT-Backend.git backend-repo
   cd backend-repo
   ```

2. **Copy Backend Files**
   - Copy all files from `web_version/server/` directory to the repository root
   - Include: `server.js`, `simple-server.js`, `package.json`, `routes/`, `utils/`, etc.

3. **Create Environment File**
   ```bash
   # Create .env.example
   NODE_ENV=development
   PORT=3000
   USE_SQLITE=true
   JWT_SECRET=your_jwt_secret_here
   ALLOWED_ORIGINS=http://localhost:8081,https://lesavot.vercel.app
   ```

4. **Commit and Push**
   ```bash
   git add .
   git commit -m "Backend API deployment - Authentication system"
   git push origin main
   ```

## 📁 File Structure After Deployment

### Frontend Repository Structure
```
LASAVOT/
├── auth.html
├── text_stego.html
├── image_stego.html
├── audio_stego.html
├── profile.html
├── history.html
├── simple-auth.js
├── config.js
├── styles.css
├── manifest.json
├── service-worker.js
└── README.md
```

### Backend Repository Structure
```
LASAVOT-Backend/
├── server.js
├── simple-server.js
├── package.json
├── .env.example
├── routes/
│   ├── auth.js
│   ├── users.js
│   └── steganography.js
├── utils/
│   ├── database.js
│   ├── logger.js
│   └── errorHandler.js
├── database/
│   └── lesavot.db
└── README.md
```

## 🔗 Repository Links

After deployment, your repositories will be available at:

- **Frontend**: https://github.com/Bechi-cyber/LASAVOT
- **Backend**: https://github.com/Bechi-cyber/LASAVOT-Backend

## 🌐 Live Deployment URLs

The repositories are configured for automatic deployment to:

- **Frontend Production**: https://lesavot.vercel.app
- **Backend Production**: https://lasavot-backend.onrender.com

## ✅ Verification Steps

After deployment, verify everything is working:

1. **Check Repository Contents**
   - Visit both GitHub repositories
   - Ensure all files are present
   - Check README files are properly formatted

2. **Test Live URLs**
   - Frontend: https://lesavot.vercel.app
   - Backend Health: https://lasavot-backend.onrender.com/api/health

3. **Test Authentication Flow**
   - Go to https://lesavot.vercel.app/auth.html
   - Try signing up with a test account
   - Verify signin works properly

## 🛠️ Troubleshooting

### Common Issues

1. **Git Authentication**
   - Ensure you're logged into GitHub
   - Use personal access token if needed
   - Check repository permissions

2. **PowerShell Execution Policy**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **Repository Not Found**
   - Ensure repositories exist on GitHub
   - Check repository names are correct
   - Verify you have push access

### Manual Git Commands

If scripts fail, use these commands:

```bash
# Initialize repository
git init
git remote add origin <repository-url>

# Add and commit files
git add .
git commit -m "Initial deployment"

# Push to GitHub
git push -u origin main
```

## 📞 Support

If you encounter issues:
1. Check the error messages in the PowerShell output
2. Verify Git is installed and configured
3. Ensure you have access to the GitHub repositories
4. Try the manual deployment steps above

---

**Last Updated**: July 7, 2025
**Status**: Ready for Deployment
**Repositories**: Frontend + Backend Separation Complete
