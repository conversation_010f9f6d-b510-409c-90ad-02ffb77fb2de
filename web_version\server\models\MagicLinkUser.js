/**
 * Magic Link User Model
 * 
 * Handles user authentication using Magic Link system.
 * Users authenticate using email-only with JWT magic links sent via email.
 */

const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const database = require('../utils/database');
const logger = require('../utils/logger');

class MagicLinkUser {
  constructor(data = {}) {
    this.id = data.id || null;
    this.email = data.email || '';
    this.displayName = data.display_name || '';
    this.isVerified = data.is_verified !== undefined ? data.is_verified : false;
    this.magicLinkToken = data.magic_link_token || null;
    this.magicLinkExpires = data.magic_link_expires || null;
    this.failedAttempts = data.failed_attempts || 0;
    this.accountLockedUntil = data.account_locked_until || null;
    this.lastLogin = data.last_login || null;
    this.createdAt = data.created_at || null;
    this.updatedAt = data.updated_at || null;
  }

  /**
   * Generate Magic Link JWT Token
   */
  generateMagicLinkToken() {
    const payload = {
      email: this.email,
      type: 'magic_link',
      timestamp: Date.now()
    };

    const token = jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: '15m', // Magic links expire in 15 minutes
      issuer: 'lesavot-auth',
      audience: 'lesavot-users'
    });

    // Set expiration time (15 minutes from now)
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
    
    this.magicLinkToken = token;
    this.magicLinkExpires = expiresAt;

    return token;
  }

  /**
   * Verify Magic Link Token
   */
  static verifyMagicLinkToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET, {
        issuer: 'lesavot-auth',
        audience: 'lesavot-users'
      });

      return {
        valid: true,
        email: decoded.email,
        type: decoded.type,
        timestamp: decoded.timestamp
      };
    } catch (error) {
      logger.error('Magic link token verification failed:', error.message);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Save user to database
   */
  async save() {
    try {
      logger.info(`Saving user: ${this.email}`, {
        id: this.id,
        hasToken: !!this.magicLinkToken,
        tokenExpires: this.magicLinkExpires
      });

      if (this.id) {
        // Update existing user
        logger.info('Updating existing user');
        const query = `
          UPDATE users
          SET email = $1, display_name = $2, is_verified = $3,
              magic_link_token = $4, magic_link_expires = $5,
              failed_attempts = $6, account_locked_until = $7,
              last_login = $8, updated_at = CURRENT_TIMESTAMP
          WHERE id = $9
          RETURNING *
        `;

        const values = [
          this.email, this.displayName, this.isVerified,
          this.magicLinkToken, this.magicLinkExpires,
          this.failedAttempts, this.accountLockedUntil,
          this.lastLogin, this.id
        ];

        const result = await database.query(query, values);
        logger.info('Update result:', result.rows[0]);
        return result.rows[0];
      } else {
        // Create new user
        const username = this.email.split('@')[0]; // Generate username from email
        const displayName = this.displayName || username;

        const query = `
          INSERT INTO users (username, email, display_name, is_verified, magic_link_token, magic_link_expires, password_hash)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING *
        `;

        const values = [
          username,
          this.email,
          displayName,
          this.isVerified,
          this.magicLinkToken,
          this.magicLinkExpires,
          'magic_link_auth' // Placeholder for password_hash since it's required
        ];

        const result = await database.query(query, values);
        const userData = result.rows[0];
        this.id = userData.id;
        this.createdAt = userData.created_at;
        this.updatedAt = userData.updated_at;
        
        return userData;
      }
    } catch (error) {
      logger.error('Error saving user:', error.message);
      throw error;
    }
  }

  /**
   * Find user by email
   */
  static async findByEmail(email) {
    try {
      const query = 'SELECT * FROM users WHERE email = $1';
      const result = await database.query(query, [email]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new MagicLinkUser(result.rows[0]);
    } catch (error) {
      logger.error('Error finding user by email:', error.message);
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  static async findById(id) {
    try {
      const query = 'SELECT * FROM users WHERE id = $1';
      const result = await database.query(query, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return new MagicLinkUser(result.rows[0]);
    } catch (error) {
      logger.error('Error finding user by ID:', error.message);
      throw error;
    }
  }

  /**
   * Find user by magic link token
   */
  static async findByMagicToken(token) {
    try {
      // Get current timestamp in milliseconds for comparison
      const currentTimestamp = Date.now();

      const query = `
        SELECT * FROM users
        WHERE magic_link_token = $1
        AND magic_link_expires > $2
      `;
      const result = await database.query(query, [token, currentTimestamp]);
      logger.info(`Query result: found ${result.rows.length} rows for token verification`);

      if (result.rows.length === 0) {
        return null;
      }

      return new MagicLinkUser(result.rows[0]);
    } catch (error) {
      logger.error('Error finding user by magic token:', error.message);
      throw error;
    }
  }

  /**
   * Clear magic link token after use
   */
  async clearMagicToken() {
    try {
      const query = `
        UPDATE users 
        SET magic_link_token = NULL, magic_link_expires = NULL,
            is_verified = TRUE, last_login = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `;
      
      const result = await database.query(query, [this.id]);
      
      // Update instance properties
      this.magicLinkToken = null;
      this.magicLinkExpires = null;
      this.isVerified = true;
      this.lastLogin = new Date();
      
      return result.rows[0];
    } catch (error) {
      logger.error('Error clearing magic token:', error.message);
      throw error;
    }
  }

  /**
   * Check if account is locked
   */
  isAccountLocked() {
    if (!this.accountLockedUntil) return false;
    return new Date() < new Date(this.accountLockedUntil);
  }

  /**
   * Increment failed attempts and lock account if necessary
   */
  async incrementFailedAttempts() {
    try {
      this.failedAttempts += 1;
      
      // Lock account after 5 failed attempts for 30 minutes
      if (this.failedAttempts >= 5) {
        this.accountLockedUntil = new Date(Date.now() + 30 * 60 * 1000);
      }

      const query = `
        UPDATE users 
        SET failed_attempts = $1, account_locked_until = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `;
      
      await database.query(query, [this.failedAttempts, this.accountLockedUntil, this.id]);
    } catch (error) {
      logger.error('Error incrementing failed attempts:', error.message);
      throw error;
    }
  }

  /**
   * Reset failed attempts on successful authentication
   */
  async resetFailedAttempts() {
    try {
      this.failedAttempts = 0;
      this.accountLockedUntil = null;

      const query = `
        UPDATE users 
        SET failed_attempts = 0, account_locked_until = NULL, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `;
      
      await database.query(query, [this.id]);
    } catch (error) {
      logger.error('Error resetting failed attempts:', error.message);
      throw error;
    }
  }

  /**
   * Convert to JSON (exclude sensitive data)
   */
  toJSON() {
    return {
      id: this.id,
      email: this.email,
      displayName: this.displayName,
      isVerified: this.isVerified,
      lastLogin: this.lastLogin,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = MagicLinkUser;
