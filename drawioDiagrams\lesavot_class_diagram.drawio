<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="abc123" version="22.1.16" type="device">
  <diagram name="LESAVOT Class Diagram" id="class-diagram">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- User Class -->
        <mxCell id="User" value="User" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="200" height="180" as="geometry" />
        </mxCell>
        <mxCell id="User-attrs" value="- userID: String&#xa;- username: String&#xa;- email: String&#xa;- passwordHash: String&#xa;- creationDate: Date&#xa;- lastLoginDate: Date" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="User">
          <mxGeometry y="26" width="200" height="94" as="geometry" />
        </mxCell>
        <mxCell id="User-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="User">
          <mxGeometry y="120" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="User-methods" value="+ register(): boolean&#xa;+ login(): boolean&#xa;+ logout(): void&#xa;+ updateProfile(): boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="User">
          <mxGeometry y="128" width="200" height="52" as="geometry" />
        </mxCell>

        <!-- Abstract StegOperation Class -->
        <mxCell id="StegOperation" value="&lt;&lt;abstract&gt;&gt;&#xa;StegOperation" style="swimlane;fontStyle=3;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=40;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="350" y="50" width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="StegOperation-attrs" value="- operationID: String&#xa;- userID: String&#xa;- operationType: String&#xa;- timestamp: Date&#xa;- password: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="StegOperation">
          <mxGeometry y="40" width="220" height="80" as="geometry" />
        </mxCell>
        <mxCell id="StegOperation-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="StegOperation">
          <mxGeometry y="120" width="220" height="8" as="geometry" />
        </mxCell>
        <mxCell id="StegOperation-methods" value="+ encrypt(): boolean&#xa;+ decrypt(): String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontStyle=2" vertex="1" parent="StegOperation">
          <mxGeometry y="128" width="220" height="32" as="geometry" />
        </mxCell>

        <!-- TextSteganography Class -->
        <mxCell id="TextSteg" value="TextSteganography" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="50" y="300" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="TextSteg-attrs" value="- textPath: String&#xa;- message: String&#xa;- outputPath: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="TextSteg">
          <mxGeometry y="26" width="200" height="54" as="geometry" />
        </mxCell>
        <mxCell id="TextSteg-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="TextSteg">
          <mxGeometry y="80" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="TextSteg-methods" value="+ embed(): boolean&#xa;+ extract(): String&#xa;+ validateText(): boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="TextSteg">
          <mxGeometry y="88" width="200" height="52" as="geometry" />
        </mxCell>

        <!-- ImageSteganography Class -->
        <mxCell id="ImageSteg" value="ImageSteganography" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="300" y="300" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="ImageSteg-attrs" value="- imagePath: String&#xa;- message: String&#xa;- outputPath: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="ImageSteg">
          <mxGeometry y="26" width="200" height="54" as="geometry" />
        </mxCell>
        <mxCell id="ImageSteg-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="ImageSteg">
          <mxGeometry y="80" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="ImageSteg-methods" value="+ embed(): boolean&#xa;+ extract(): String&#xa;+ validateImage(): boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="ImageSteg">
          <mxGeometry y="88" width="200" height="52" as="geometry" />
        </mxCell>

        <!-- AudioSteganography Class -->
        <mxCell id="AudioSteg" value="AudioSteganography" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="550" y="300" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="AudioSteg-attrs" value="- audioPath: String&#xa;- message: String&#xa;- outputPath: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="AudioSteg">
          <mxGeometry y="26" width="200" height="54" as="geometry" />
        </mxCell>
        <mxCell id="AudioSteg-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="AudioSteg">
          <mxGeometry y="80" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="AudioSteg-methods" value="+ embed(): boolean&#xa;+ extract(): String&#xa;+ validateAudio(): boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="AudioSteg">
          <mxGeometry y="88" width="200" height="52" as="geometry" />
        </mxCell>

        <!-- MultimodalSteganography Class -->
        <mxCell id="MultiSteg" value="MultimodalSteganography" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="800" y="50" width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="MultiSteg-attrs" value="- textSteg: TextSteganography&#xa;- imageSteg: ImageSteganography&#xa;- audioSteg: AudioSteganography&#xa;- distributionStrategy: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="MultiSteg">
          <mxGeometry y="26" width="220" height="74" as="geometry" />
        </mxCell>
        <mxCell id="MultiSteg-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="MultiSteg">
          <mxGeometry y="100" width="220" height="8" as="geometry" />
        </mxCell>
        <mxCell id="MultiSteg-methods" value="+ distributeMessage(): boolean&#xa;+ combineResults(): String&#xa;+ coordinateOperations(): void" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="MultiSteg">
          <mxGeometry y="108" width="220" height="52" as="geometry" />
        </mxCell>

        <!-- Authentication Class -->
        <mxCell id="Auth" value="Authentication" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="800" y="300" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="Auth-attrs" value="- sessionID: String&#xa;- isAuthenticated: boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="Auth">
          <mxGeometry y="26" width="200" height="34" as="geometry" />
        </mxCell>
        <mxCell id="Auth-line" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="Auth">
          <mxGeometry y="60" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="Auth-methods" value="+ validateCredentials(): boolean&#xa;+ createSession(): String&#xa;+ destroySession(): void" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="Auth">
          <mxGeometry y="68" width="200" height="52" as="geometry" />
        </mxCell>

        <!-- Relationships -->
        <!-- User to StegOperation (1 to many) -->
        <mxCell id="user-steg-rel" value="" style="endArrow=open;endFill=1;endSize=12;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="User-attrs" target="StegOperation-attrs">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="270" y="120" as="sourcePoint" />
            <mxPoint x="330" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="user-steg-label" value="1" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;" connectable="0" vertex="1" parent="user-steg-rel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        <mxCell id="user-steg-label2" value="*" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;" connectable="0" vertex="1" parent="user-steg-rel">
          <mxGeometry x="1" relative="1" as="geometry" />
        </mxCell>

        <!-- Inheritance relationships -->
        <!-- TextSteg extends StegOperation -->
        <mxCell id="text-inherit" value="" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="TextSteg" target="StegOperation">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="150" y="280" as="sourcePoint" />
            <mxPoint x="405" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- ImageSteg extends StegOperation -->
        <mxCell id="image-inherit" value="" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="ImageSteg" target="StegOperation">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="400" y="280" as="sourcePoint" />
            <mxPoint x="460" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- AudioSteg extends StegOperation -->
        <mxCell id="audio-inherit" value="" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="AudioSteg" target="StegOperation">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="650" y="280" as="sourcePoint" />
            <mxPoint x="515" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- MultiSteg composition relationships -->
        <mxCell id="multi-text" value="" style="endArrow=diamond;endFill=1;endSize=12;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="MultiSteg-attrs" target="TextSteg">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="780" y="350" as="sourcePoint" />
            <mxPoint x="270" y="350" as="targetPoint" />
            <Array as="points">
              <mxPoint x="750" y="113" />
              <mxPoint x="750" y="250" />
              <mxPoint x="280" y="250" />
              <mxPoint x="280" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- User to Auth relationship -->
        <mxCell id="user-auth" value="" style="endArrow=open;endFill=1;endSize=12;html=1;rounded=0;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="User" target="Auth">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="270" y="250" as="sourcePoint" />
            <mxPoint x="780" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="400" y="230" />
              <mxPoint x="700" y="230" />
              <mxPoint x="700" y="280" />
            </Array>
          </mxGeometry>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
