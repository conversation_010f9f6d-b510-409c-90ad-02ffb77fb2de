/**
 * LESAVOT - Custom Magic Link Authentication System
 *
 * This module provides passwordless authentication using custom magic links
 * sent via email with JWT token verification
 */

class MagicAuth {
    constructor() {
        this.apiBaseUrl = (window.CONFIG && window.CONFIG.apiBaseUrl) || window.API_BASE_URL || 'http://localhost:3000';
        this.currentUser = null;
        this.isInitialized = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
        this.isInitialized = true;
        console.log('Custom Magic Link authentication initialized successfully');
    }

    bindEvents() {
        // Sign in form
        const signInBtn = document.getElementById('signInBtn');
        if (signInBtn) {
            signInBtn.addEventListener('click', () => this.handleSignIn());
        }

        // Sign up form
        const signUpBtn = document.getElementById('signUpBtn');
        if (signUpBtn) {
            signUpBtn.addEventListener('click', () => this.handleSignUp());
        }

        // Form switching
        const showSignUpBtn = document.getElementById('showSignUpBtn');
        const showSignInBtn = document.getElementById('showSignInBtn');

        if (showSignUpBtn) {
            showSignUpBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSignUpForm();
            });
        }

        if (showSignInBtn) {
            showSignInBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSignInForm();
            });
        }

        // Enter key handling
        const signInEmail = document.getElementById('signInEmail');
        const signUpEmail = document.getElementById('signUpEmail');

        if (signInEmail) {
            signInEmail.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.handleSignIn();
            });
        }

        if (signUpEmail) {
            signUpEmail.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.handleSignUp();
            });
        }

        // Terms and Privacy Policy modal handlers
        this.bindModalEvents();
    }

    bindModalEvents() {
        const showTermsBtn = document.getElementById('showTermsBtn');
        const showPrivacyBtn = document.getElementById('showPrivacyBtn');
        const termsModal = document.getElementById('termsModal');
        const privacyModal = document.getElementById('privacyModal');

        if (showTermsBtn && termsModal) {
            showTermsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                termsModal.style.display = 'block';
            });
        }

        if (showPrivacyBtn && privacyModal) {
            showPrivacyBtn.addEventListener('click', (e) => {
                e.preventDefault();
                privacyModal.style.display = 'block';
            });
        }

        // Modal close handlers
        document.querySelectorAll('.modal-close-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });

        // Accept terms button
        const acceptTermsBtn = document.getElementById('acceptTermsBtn');
        if (acceptTermsBtn) {
            acceptTermsBtn.addEventListener('click', () => {
                document.getElementById('agreeTerms').checked = true;
                document.getElementById('termsModal').style.display = 'none';
            });
        }
    }

    async handleSignIn() {
        const email = document.getElementById('signInEmail').value.trim();
        const btn = document.getElementById('signInBtn');
        const status = document.getElementById('signInStatus');

        if (!this.validateEmail(email)) {
            this.showError('Please enter a valid email address');
            return;
        }

        try {
            this.setLoading(btn, true, 'Sending Magic Link...');

            const response = await fetch(`${this.apiBaseUrl}/magic-auth/signin/request-magic-link`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                this.showStatus(status, 'success', 'Magic link sent! Check your email and click the link to sign in.');
                document.getElementById('signInEmail').value = '';
            } else {
                this.showError(data.message || 'Failed to send magic link');
            }
        } catch (error) {
            console.error('Sign in error:', error);
            this.showError('Network error. Please check your connection and try again.');
        } finally {
            this.setLoading(btn, false, 'Send Magic Link');
        }
    }

    async handleSignUp() {
        const email = document.getElementById('signUpEmail').value.trim();
        const agreeTerms = document.getElementById('agreeTerms').checked;
        const btn = document.getElementById('signUpBtn');
        const status = document.getElementById('signUpStatus');

        if (!this.validateEmail(email)) {
            this.showError('Please enter a valid email address');
            return;
        }

        if (!agreeTerms) {
            this.showError('Please agree to the Terms of Service and Privacy Policy');
            return;
        }

        try {
            this.setLoading(btn, true, 'Sending Magic Link...');

            const response = await fetch(`${this.apiBaseUrl}/magic-auth/signup/request-magic-link`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                this.showStatus(status, 'success', 'Magic link sent! Check your email and click the link to complete your signup.');
                document.getElementById('signUpEmail').value = '';
                document.getElementById('agreeTerms').checked = false;
            } else {
                this.showError(data.message || 'Failed to send magic link');
            }
        } catch (error) {
            console.error('Sign up error:', error);
            this.showError('Network error. Please check your connection and try again.');
        } finally {
            this.setLoading(btn, false, 'Send Magic Link');
        }
    }

    showSignUpForm() {
        document.getElementById('signInForm').classList.remove('active');
        document.getElementById('signUpForm').classList.add('active');
        this.clearMessages();
    }

    showSignInForm() {
        document.getElementById('signUpForm').classList.remove('active');
        document.getElementById('signInForm').classList.add('active');
        this.clearMessages();
    }

    validateEmail(email) {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(email);
    }

    setLoading(button, loading, text) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
        } else {
            button.disabled = false;
            button.innerHTML = `<i class="fas fa-paper-plane"></i> ${text}`;
        }
    }

    showStatus(statusElement, type, message) {
        if (statusElement) {
            statusElement.style.display = 'block';
            statusElement.className = `magic-link-status show ${type}`;

            const content = statusElement.querySelector('.status-content p');
            if (content) {
                content.textContent = message;
            }
        }
    }

    showError(message) {
        // Create or update error display
        let errorDiv = document.querySelector('.auth-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'auth-error';
            errorDiv.style.cssText = `
                background: #ffebee;
                color: #c62828;
                padding: 15px;
                border-radius: 8px;
                margin: 15px 0;
                border: 1px solid #ef9a9a;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
            `;

            const activeForm = document.querySelector('.auth-form.active .auth-body');
            if (activeForm) {
                activeForm.insertBefore(errorDiv, activeForm.firstChild);
            }
        }

        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> <span>${message}</span>`;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorDiv && errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    clearMessages() {
        // Clear error messages
        const errorDiv = document.querySelector('.auth-error');
        if (errorDiv && errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }

        // Hide status messages
        const statusElements = document.querySelectorAll('.magic-link-status');
        statusElements.forEach(el => {
            el.style.display = 'none';
            el.classList.remove('show');
        });
    }

    async checkAuthStatus() {
        const token = localStorage.getItem('sessionToken');
        if (token) {
            try {
                const response = await fetch(`${this.apiBaseUrl}/magic-auth/status`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (response.ok && data.data.authenticated) {
                    // User is already authenticated, redirect to main app
                    window.location.href = 'index.html';
                }
            } catch (error) {
                console.error('Auth status check failed:', error);
                // Clear invalid token
                localStorage.removeItem('sessionToken');
                localStorage.removeItem('user');
            }
        }
    }

    // Utility method to get current user
    getCurrentUser() {
        const userStr = localStorage.getItem('user');
        return userStr ? JSON.parse(userStr) : null;
    }

    // Utility method to check if user is authenticated
    isAuthenticated() {
        return !!localStorage.getItem('sessionToken');
    }

    // Logout method
    logout() {
        localStorage.removeItem('sessionToken');
        localStorage.removeItem('user');
        window.location.href = 'auth.html';
    }

    // Compatibility methods for existing code
    async register(userData) {
        // For compatibility - redirect to signup form
        this.showSignUpForm();
        if (userData && userData.email) {
            const emailField = document.getElementById('signUpEmail');
            if (emailField) {
                emailField.value = userData.email;
            }
        }
    }

    async login(email, password = null) {
        // For compatibility - redirect to signin form
        this.showSignInForm();
        if (email) {
            const emailField = document.getElementById('signInEmail');
            if (emailField) {
                emailField.value = email;
            }
        }
    }

    // Get user info in format expected by existing code
    getUserInfo() {
        const user = this.getCurrentUser();
        if (!user) return null;

        return {
            id: user.id,
            email: user.email,
            username: user.display_name || user.email.split('@')[0],
            fullName: user.display_name || user.email,
            displayName: user.display_name
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MagicAuth();
});

// Create global instance for compatibility
const magicAuth = new MagicAuth();

// Make it available globally for compatibility
window.magicAuth = magicAuth;
window.userAuth = magicAuth; // For backward compatibility

console.log('Custom Magic Link authentication system loaded successfully');
