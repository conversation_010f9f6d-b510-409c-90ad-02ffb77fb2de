<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic.link Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #007bff;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🪄 Magic.link Authentication Test</h1>
        <p>This page tests the Magic.link authentication integration for LESAVOT.</p>

        <!-- Login Test Section -->
        <div class="test-section">
            <h3>🔐 Login Test</h3>
            <div class="form-group">
                <label for="loginEmail">Email Address:</label>
                <input type="email" id="loginEmail" placeholder="Enter your email" value="<EMAIL>">
            </div>
            <button id="loginBtn">Send Magic Link</button>
            <button id="checkStatusBtn">Check Login Status</button>
            <div id="loginStatus" class="status" style="display: none;"></div>
            <div id="userInfo" class="user-info" style="display: none;"></div>
        </div>

        <!-- Logout Test Section -->
        <div class="test-section">
            <h3>🚪 Logout Test</h3>
            <button id="logoutBtn">Logout</button>
            <div id="logoutStatus" class="status" style="display: none;"></div>
        </div>

        <!-- System Status Section -->
        <div class="test-section">
            <h3>⚙️ System Status</h3>
            <button id="systemCheckBtn">Check Magic.link Status</button>
            <div id="systemStatus" class="status" style="display: none;"></div>
        </div>

        <a href="auth.html" class="back-link">← Back to Main Auth Page</a>
    </div>

    <!-- Load Magic.link SDK -->
    <script src="https://auth.magic.link/sdk"></script>
    <script src="magic-auth.js"></script>

    <script>
        // Test functionality
        document.addEventListener('DOMContentLoaded', function() {
            const loginBtn = document.getElementById('loginBtn');
            const checkStatusBtn = document.getElementById('checkStatusBtn');
            const logoutBtn = document.getElementById('logoutBtn');
            const systemCheckBtn = document.getElementById('systemCheckBtn');

            // Login test
            loginBtn.addEventListener('click', async function() {
                const email = document.getElementById('loginEmail').value;
                const statusDiv = document.getElementById('loginStatus');
                
                if (!email) {
                    showStatus(statusDiv, 'Please enter an email address', 'error');
                    return;
                }

                try {
                    loginBtn.disabled = true;
                    showStatus(statusDiv, 'Sending magic link...', 'info');

                    // Wait for Magic.link to initialize
                    if (!window.magicAuth || !window.magicAuth.isInitialized) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }

                    const result = await window.magicAuth.loginWithEmail(email);
                    
                    if (result.success) {
                        showStatus(statusDiv, 'Magic link sent successfully! Check your email.', 'success');
                        updateUserInfo(result.user);
                    } else {
                        showStatus(statusDiv, 'Failed to send magic link', 'error');
                    }
                } catch (error) {
                    console.error('Login test error:', error);
                    showStatus(statusDiv, `Error: ${error.message}`, 'error');
                } finally {
                    loginBtn.disabled = false;
                }
            });

            // Check status test
            checkStatusBtn.addEventListener('click', async function() {
                const statusDiv = document.getElementById('loginStatus');
                
                try {
                    const isLoggedIn = await window.magicAuth.isLoggedIn();
                    const currentUser = await window.magicAuth.getCurrentUser();
                    
                    if (isLoggedIn && currentUser) {
                        showStatus(statusDiv, 'User is logged in', 'success');
                        updateUserInfo(currentUser);
                    } else {
                        showStatus(statusDiv, 'User is not logged in', 'info');
                        document.getElementById('userInfo').style.display = 'none';
                    }
                } catch (error) {
                    console.error('Status check error:', error);
                    showStatus(statusDiv, `Error checking status: ${error.message}`, 'error');
                }
            });

            // Logout test
            logoutBtn.addEventListener('click', async function() {
                const statusDiv = document.getElementById('logoutStatus');
                
                try {
                    const result = await window.magicAuth.logout();
                    
                    if (result.success) {
                        showStatus(statusDiv, 'Successfully logged out', 'success');
                        document.getElementById('userInfo').style.display = 'none';
                    } else {
                        showStatus(statusDiv, 'Logout failed', 'error');
                    }
                } catch (error) {
                    console.error('Logout test error:', error);
                    showStatus(statusDiv, `Error: ${error.message}`, 'error');
                }
            });

            // System check test
            systemCheckBtn.addEventListener('click', function() {
                const statusDiv = document.getElementById('systemStatus');
                
                let statusText = '';
                if (typeof Magic !== 'undefined') {
                    statusText += '✅ Magic SDK loaded\n';
                } else {
                    statusText += '❌ Magic SDK not loaded\n';
                }

                if (window.magicAuth) {
                    statusText += '✅ MagicAuth instance created\n';
                    statusText += `✅ Initialized: ${window.magicAuth.isInitialized}\n`;
                } else {
                    statusText += '❌ MagicAuth instance not found\n';
                }

                showStatus(statusDiv, statusText, 'info');
            });

            // Helper functions
            function showStatus(element, message, type) {
                element.textContent = message;
                element.className = `status ${type}`;
                element.style.display = 'block';
            }

            function updateUserInfo(user) {
                const userInfoDiv = document.getElementById('userInfo');
                if (user) {
                    userInfoDiv.innerHTML = `
                        <h4>User Information:</h4>
                        <p><strong>Email:</strong> ${user.email || 'N/A'}</p>
                        <p><strong>Public Address:</strong> ${user.publicAddress || 'N/A'}</p>
                        <p><strong>Issuer:</strong> ${user.issuer || 'N/A'}</p>
                    `;
                    userInfoDiv.style.display = 'block';
                } else {
                    userInfoDiv.style.display = 'none';
                }
            }

            // Auto-check system status on load
            setTimeout(() => {
                systemCheckBtn.click();
            }, 1000);
        });
    </script>
</body>
</html>
