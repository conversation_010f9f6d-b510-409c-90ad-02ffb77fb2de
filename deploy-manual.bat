@echo off
echo ========================================
echo LESAVOT Manual Deployment Guide
echo ========================================
echo.
echo This will create deployment directories and show you the commands to run.
echo.
echo Target Repositories:
echo 1. Frontend: https://github.com/Bechi-cyber/LASAVOT
echo 2. Backend:  https://github.com/Bechi-cyber/LASAVOT-Backend
echo.
pause

echo.
echo ========================================
echo CREATING DEPLOYMENT DIRECTORIES
echo ========================================

REM Create frontend deployment directory
if exist "deploy_frontend" rmdir /s /q "deploy_frontend"
mkdir "deploy_frontend"
echo ✅ Created deploy_frontend directory

REM Create backend deployment directory  
if exist "deploy_backend" rmdir /s /q "deploy_backend"
mkdir "deploy_backend"
echo ✅ Created deploy_backend directory

echo.
echo ========================================
echo COPYING FILES
echo ========================================

REM Copy frontend files
echo Copying frontend files...
xcopy "web_version\*" "deploy_frontend\" /E /I /Y
echo ✅ Frontend files copied

REM Copy backend files
echo Copying backend files...
xcopy "web_version\server\*" "deploy_backend\" /E /I /Y
echo ✅ Backend files copied

echo.
echo ========================================
echo CREATING README FILES
echo ========================================

REM Create frontend README
echo # LESAVOT Frontend > "deploy_frontend\README.md"
echo. >> "deploy_frontend\README.md"
echo Advanced Multimodal Steganography Platform - Frontend >> "deploy_frontend\README.md"
echo. >> "deploy_frontend\README.md"
echo ## Features >> "deploy_frontend\README.md"
echo - Secure authentication system >> "deploy_frontend\README.md"
echo - Text, Image, and Audio steganography >> "deploy_frontend\README.md"
echo - Modern responsive design >> "deploy_frontend\README.md"
echo - PWA support >> "deploy_frontend\README.md"
echo. >> "deploy_frontend\README.md"
echo ## Live Demo >> "deploy_frontend\README.md"
echo https://lesavot.vercel.app >> "deploy_frontend\README.md"
echo ✅ Frontend README created

REM Create backend README
echo # LESAVOT Backend API > "deploy_backend\README.md"
echo. >> "deploy_backend\README.md"
echo Backend API for LESAVOT Steganography Platform >> "deploy_backend\README.md"
echo. >> "deploy_backend\README.md"
echo ## Features >> "deploy_backend\README.md"
echo - JWT authentication >> "deploy_backend\README.md"
echo - User management >> "deploy_backend\README.md"
echo - SQLite/PostgreSQL support >> "deploy_backend\README.md"
echo - RESTful API design >> "deploy_backend\README.md"
echo. >> "deploy_backend\README.md"
echo ## API Endpoints >> "deploy_backend\README.md"
echo - POST /api/v1/auth/signup >> "deploy_backend\README.md"
echo - POST /api/v1/auth/signin >> "deploy_backend\README.md"
echo - GET /api/v1/auth/me >> "deploy_backend\README.md"
echo - GET /api/health >> "deploy_backend\README.md"
echo. >> "deploy_backend\README.md"
echo ## Live API >> "deploy_backend\README.md"
echo https://lasavot-backend.onrender.com >> "deploy_backend\README.md"
echo ✅ Backend README created

echo.
echo ========================================
echo DEPLOYMENT COMMANDS
echo ========================================
echo.
echo Now run these commands to deploy:
echo.
echo FOR FRONTEND:
echo cd deploy_frontend
echo git init
echo git remote add origin https://github.com/Bechi-cyber/LASAVOT.git
echo git add .
echo git commit -m "Frontend deployment - Authentication system"
echo git branch -M main
echo git push -u origin main
echo cd ..
echo.
echo FOR BACKEND:
echo cd deploy_backend
echo git init
echo git remote add origin https://github.com/Bechi-cyber/LASAVOT-Backend.git
echo git add .
echo git commit -m "Backend API deployment"
echo git branch -M main
echo git push -u origin main
echo cd ..
echo.
echo ========================================
echo READY FOR DEPLOYMENT
echo ========================================
echo.
echo Directories created:
echo - deploy_frontend/ (ready for frontend repo)
echo - deploy_backend/ (ready for backend repo)
echo.
echo Copy the commands above and run them in your terminal.
echo.
pause
