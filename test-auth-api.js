/**
 * Automated Authentication API Test Script
 * Tests the complete authentication flow
 */

const API_BASE = 'http://localhost:3000/api/v1';

async function testAPI() {
    console.log('🧪 Starting LESAVOT Authentication API Tests...\n');

    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    try {
        const response = await fetch('http://localhost:3000/api/health');
        const data = await response.json();
        console.log('✅ Health Check:', data.status);
        console.log('   Users:', data.users, 'Sessions:', data.sessions);
    } catch (error) {
        console.log('❌ Health Check Failed:', error.message);
        return;
    }

    // Test 2: Sign Up
    console.log('\n2️⃣ Testing Sign Up...');
    const testUser = {
        username: 'testuser_' + Date.now(),
        email: 'test_' + Date.now() + '@example.com',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!'
    };

    try {
        const response = await fetch(`${API_BASE}/auth/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testUser)
        });

        const data = await response.json();
        
        if (response.ok && data.success) {
            console.log('✅ Sign Up Successful');
            console.log('   User ID:', data.user.id);
            console.log('   Username:', data.user.username);
            console.log('   Email:', data.user.email);
        } else {
            console.log('❌ Sign Up Failed:', data.message);
            return;
        }
    } catch (error) {
        console.log('❌ Sign Up Error:', error.message);
        return;
    }

    // Test 3: Sign In
    console.log('\n3️⃣ Testing Sign In...');
    let sessionToken = null;

    try {
        const response = await fetch(`${API_BASE}/auth/signin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: testUser.username,
                password: testUser.password
            })
        });

        const data = await response.json();
        
        if (response.ok && data.success) {
            console.log('✅ Sign In Successful');
            console.log('   Session Token:', data.sessionToken.substring(0, 20) + '...');
            console.log('   User:', data.user.username);
            sessionToken = data.sessionToken;
        } else {
            console.log('❌ Sign In Failed:', data.message);
            return;
        }
    } catch (error) {
        console.log('❌ Sign In Error:', error.message);
        return;
    }

    // Test 4: Get User Info
    console.log('\n4️⃣ Testing User Info...');
    try {
        const response = await fetch(`${API_BASE}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${sessionToken}`
            }
        });

        const data = await response.json();
        
        if (response.ok && data.success) {
            console.log('✅ User Info Retrieved');
            console.log('   User ID:', data.user.id);
            console.log('   Username:', data.user.username);
            console.log('   Email:', data.user.email);
        } else {
            console.log('❌ User Info Failed:', data.message);
        }
    } catch (error) {
        console.log('❌ User Info Error:', error.message);
    }

    // Test 5: Invalid Credentials
    console.log('\n5️⃣ Testing Invalid Credentials...');
    try {
        const response = await fetch(`${API_BASE}/auth/signin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'nonexistent',
                password: 'wrongpassword'
            })
        });

        const data = await response.json();
        
        if (!response.ok && !data.success) {
            console.log('✅ Invalid Credentials Properly Rejected');
            console.log('   Message:', data.message);
        } else {
            console.log('❌ Invalid Credentials Test Failed - Should have been rejected');
        }
    } catch (error) {
        console.log('❌ Invalid Credentials Test Error:', error.message);
    }

    // Test 6: Duplicate User
    console.log('\n6️⃣ Testing Duplicate User Prevention...');
    try {
        const response = await fetch(`${API_BASE}/auth/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testUser)
        });

        const data = await response.json();
        
        if (!response.ok && !data.success) {
            console.log('✅ Duplicate User Properly Prevented');
            console.log('   Message:', data.message);
        } else {
            console.log('❌ Duplicate User Test Failed - Should have been prevented');
        }
    } catch (error) {
        console.log('❌ Duplicate User Test Error:', error.message);
    }

    // Test 7: Logout
    console.log('\n7️⃣ Testing Logout...');
    try {
        const response = await fetch(`${API_BASE}/auth/logout`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${sessionToken}`
            }
        });

        const data = await response.json();
        
        if (response.ok && data.success) {
            console.log('✅ Logout Successful');
            console.log('   Message:', data.message);
        } else {
            console.log('❌ Logout Failed:', data.message);
        }
    } catch (error) {
        console.log('❌ Logout Error:', error.message);
    }

    // Final Health Check
    console.log('\n8️⃣ Final Health Check...');
    try {
        const response = await fetch('http://localhost:3000/api/health');
        const data = await response.json();
        console.log('✅ Final Health Check:', data.status);
        console.log('   Total Users:', data.users, 'Active Sessions:', data.sessions);
    } catch (error) {
        console.log('❌ Final Health Check Failed:', error.message);
    }

    console.log('\n🎉 Authentication API Tests Completed!');
    console.log('✅ Backend server is fully functional');
    console.log('✅ All authentication endpoints working');
    console.log('✅ Ready for frontend integration');
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
    // Node.js environment
    const fetch = require('node-fetch');
    testAPI().catch(console.error);
} else {
    // Browser environment
    window.testAPI = testAPI;
    console.log('API test function available as window.testAPI()');
}
