{"name": "lesavot-api", "version": "1.0.0", "description": "Backend API for LESAVOT Steganography Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:https": "nodemon server.js", "generate-cert": "node scripts/generate-ssl-cert.js", "lint": "eslint .", "test": "jest", "migrate": "node scripts/migrate-db.js", "backup-db": "node scripts/backup-db.js", "monitor": "node scripts/monitor.js"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.4", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^3.0.2", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}