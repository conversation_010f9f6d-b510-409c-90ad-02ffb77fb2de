<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT - Working Authentication</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo p {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 10px;
        }

        .form-tabs {
            display: flex;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 5px;
        }

        .tab-btn {
            flex: 1;
            padding: 12px;
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab-btn.active {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
        }

        .form-content {
            display: none;
        }

        .form-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .input-group {
            position: relative;
        }

        .input-group input {
            width: 100%;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: #fff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        }

        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            font-size: 18px;
        }

        .password-strength {
            margin-top: 10px;
            display: flex;
            gap: 5px;
        }

        .strength-bar {
            height: 4px;
            flex: 1;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .strength-bar.active {
            background: #00ff88;
        }

        .auth-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            border: none;
            border-radius: 10px;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .auth-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-weight: 500;
        }

        .message.success {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid rgba(0, 255, 136, 0.3);
            color: #00ff88;
        }

        .message.error {
            background: rgba(255, 82, 82, 0.2);
            border: 1px solid rgba(255, 82, 82, 0.3);
            color: #ff5252;
        }

        .message.loading {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #00d4ff;
        }

        .footer-links {
            text-align: center;
            margin-top: 30px;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            margin: 0 15px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #00d4ff;
        }

        .debug-info {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>LESAVOT</h1>
            <p>Secure Steganography Platform</p>
        </div>

        <div class="form-tabs">
            <button class="tab-btn active" onclick="switchTab('signin')">Sign In</button>
            <button class="tab-btn" onclick="switchTab('signup')">Sign Up</button>
        </div>

        <!-- Sign In Form -->
        <div id="signin-form" class="form-content active">
            <form id="signinForm">
                <div class="form-group">
                    <label for="signinUsername">Username</label>
                    <div class="input-group">
                        <input type="text" id="signinUsername" placeholder="Enter your username" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="signinPassword">Password</label>
                    <div class="input-group">
                        <input type="password" id="signinPassword" placeholder="Enter your password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('signinPassword')">
                            👁️
                        </button>
                    </div>
                </div>
                <button type="submit" class="auth-btn" id="signinBtn">Sign In</button>
            </form>
        </div>

        <!-- Sign Up Form -->
        <div id="signup-form" class="form-content">
            <form id="signupForm">
                <div class="form-group">
                    <label for="signupUsername">Username</label>
                    <div class="input-group">
                        <input type="text" id="signupUsername" placeholder="Choose a username" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="signupEmail">Email</label>
                    <div class="input-group">
                        <input type="email" id="signupEmail" placeholder="Enter your email" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="signupPassword">Password</label>
                    <div class="input-group">
                        <input type="password" id="signupPassword" placeholder="Create a password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('signupPassword')">
                            👁️
                        </button>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar"></div>
                        <div class="strength-bar"></div>
                        <div class="strength-bar"></div>
                        <div class="strength-bar"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <div class="input-group">
                        <input type="password" id="confirmPassword" placeholder="Confirm your password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                            👁️
                        </button>
                    </div>
                </div>
                <button type="submit" class="auth-btn" id="signupBtn">Create Account</button>
            </form>
        </div>

        <div id="message-container"></div>

        <div class="footer-links">
            <a href="index.html">← Back to Home</a>
            <a href="#" onclick="showDebug()">Debug Info</a>
        </div>
    </div>

    <div id="debug-info" class="debug-info" style="display: none;"></div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3000/api/v1';
        let debugMode = false;

        // Tab switching
        function switchTab(tab) {
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update form content
            document.querySelectorAll('.form-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tab + '-form').classList.add('active');

            // Clear messages
            clearMessage();
        }

        // Password visibility toggle
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const button = input.nextElementSibling;
            
            if (input.type === 'password') {
                input.type = 'text';
                button.textContent = '🙈';
            } else {
                input.type = 'password';
                button.textContent = '👁️';
            }
        }

        // Password strength meter
        function updatePasswordStrength(password) {
            const bars = document.querySelectorAll('.strength-bar');
            let strength = 0;

            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            bars.forEach((bar, index) => {
                bar.classList.toggle('active', index < strength);
            });
        }

        // Message display
        function showMessage(text, type = 'info') {
            const container = document.getElementById('message-container');
            container.innerHTML = `<div class="message ${type}">${text}</div>`;
        }

        function clearMessage() {
            document.getElementById('message-container').innerHTML = '';
        }

        // Debug logging
        function debugLog(message) {
            if (debugMode) {
                const debugDiv = document.getElementById('debug-info');
                const timestamp = new Date().toLocaleTimeString();
                debugDiv.innerHTML += `[${timestamp}] ${message}<br>`;
                debugDiv.scrollTop = debugDiv.scrollHeight;
            }
            console.log(message);
        }

        function showDebug() {
            debugMode = !debugMode;
            const debugDiv = document.getElementById('debug-info');
            debugDiv.style.display = debugMode ? 'block' : 'none';
            if (debugMode) {
                debugLog('Debug mode enabled');
                debugLog(`API Base: ${API_BASE}`);
            }
        }

        // Sign Up Handler
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('signupUsername').value.trim();
            const email = document.getElementById('signupEmail').value.trim();
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            debugLog(`Signup attempt: ${username}`);

            // Validation
            if (password !== confirmPassword) {
                showMessage('Passwords do not match', 'error');
                return;
            }

            if (password.length < 8) {
                showMessage('Password must be at least 8 characters long', 'error');
                return;
            }

            const signupBtn = document.getElementById('signupBtn');
            signupBtn.disabled = true;
            showMessage('Creating account...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        confirmPassword
                    })
                });

                debugLog(`Signup response: ${response.status}`);
                const data = await response.json();
                debugLog(`Signup data: ${JSON.stringify(data)}`);

                if (response.ok && data.success) {
                    showMessage('Account created successfully! Please sign in.', 'success');
                    
                    // Switch to sign in and pre-fill
                    setTimeout(() => {
                        switchTab('signin');
                        document.querySelector('.tab-btn').click();
                        document.getElementById('signinUsername').value = username;
                        document.getElementById('signinPassword').value = password;
                    }, 2000);
                    
                } else {
                    showMessage(data.message || 'Failed to create account', 'error');
                }

            } catch (error) {
                debugLog(`Signup error: ${error.message}`);
                showMessage('Network error. Please try again.', 'error');
            } finally {
                signupBtn.disabled = false;
            }
        });

        // Sign In Handler
        document.getElementById('signinForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('signinUsername').value.trim();
            const password = document.getElementById('signinPassword').value;

            debugLog(`Signin attempt: ${username}`);

            const signinBtn = document.getElementById('signinBtn');
            signinBtn.disabled = true;
            showMessage('Signing in...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password
                    })
                });

                debugLog(`Signin response: ${response.status}`);
                const data = await response.json();
                debugLog(`Signin data: ${JSON.stringify(data)}`);

                if (response.ok && data.success) {
                    showMessage('Sign in successful! Redirecting...', 'success');
                    
                    // Store authentication data
                    localStorage.setItem('lesavot_token', data.sessionToken);
                    localStorage.setItem('lesavot_user', JSON.stringify(data.user));
                    
                    // Redirect to main application
                    setTimeout(() => {
                        window.location.href = 'text_stego.html';
                    }, 2000);
                    
                } else {
                    showMessage(data.message || 'Invalid credentials', 'error');
                }

            } catch (error) {
                debugLog(`Signin error: ${error.message}`);
                showMessage('Network error. Please try again.', 'error');
            } finally {
                signinBtn.disabled = false;
            }
        });

        // Password strength monitoring
        document.getElementById('signupPassword').addEventListener('input', (e) => {
            updatePasswordStrength(e.target.value);
        });

        // Auto-fill for testing
        window.addEventListener('load', () => {
            // Pre-fill with existing user for testing
            document.getElementById('signinUsername').value = 'TCHANGA BECHI Jacques';
            document.getElementById('signinPassword').value = 'TestPassword123!';
            
            debugLog('Working authentication page loaded');
        });
    </script>
</body>
</html>
