# 🛡️ LESAVOT - How to Run Guide

## Overview
LESAVOT is now available in multiple formats for maximum accessibility and security. Choose the method that best suits your needs.

---

## 🌐 Method 1: Web Browser (Recommended)

### **Option A: HTTP Server (Best Performance)**
1. **Open Terminal/Command Prompt**
2. **Navigate to LESAVOT folder:**
   ```bash
   cd path/to/LESAVOT/web_version
   ```
3. **Start HTTP Server:**
   ```bash
   python -m http.server 3000
   ```
4. **Open Browser:** http://localhost:3000

### **Option B: Direct File Access (Offline)**
1. **Double-click:** `web_version/index.html`
2. **Or open in browser:** `file:///path/to/LESAVOT/web_version/index.html`

---

## 🖥️ Method 2: Desktop Application

### **Quick Start (Windows)**
1. **Double-click:** `Run_LESAVOT_Desktop.bat`
2. **Follow the on-screen instructions**
3. **Application will install dependencies and launch automatically**

### **Manual Installation**
1. **Install Node.js:** https://nodejs.org/
2. **Open Terminal in desktop_app folder:**
   ```bash
   cd desktop_app
   npm install
   npm start
   ```

---

## 🚀 Method 3: Offline App Launcher

### **Universal Launcher**
1. **Double-click:** `LESAVOT_Offline_App.html`
2. **Choose your preferred launch method:**
   - Web Browser
   - Desktop Application
3. **Follow the guided setup**

---

## 📱 Method 4: Progressive Web App (PWA)

### **Install as App**
1. **Open in Chrome/Edge:** http://localhost:3000
2. **Click install button** in address bar
3. **Use like a native app**

---

## 🔧 System Requirements

### **Minimum Requirements:**
- **OS:** Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Browser:** Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **RAM:** 4GB minimum, 8GB recommended
- **Storage:** 500MB free space

### **For Desktop App:**
- **Node.js:** Version 16.0 or higher
- **Additional:** 1GB free space for dependencies

---

## 🛠️ Troubleshooting

### **Web Browser Issues:**
- **CORS Errors:** Use HTTP server method instead of file:// protocol
- **Loading Issues:** Clear browser cache and reload
- **Performance:** Use Chrome or Edge for best performance

### **Desktop App Issues:**
- **Node.js Not Found:** Install from https://nodejs.org/
- **Installation Fails:** Check internet connection and try again
- **App Won't Start:** Run `npm install` manually in desktop_app folder

### **General Issues:**
- **Files Missing:** Re-download LESAVOT from GitHub
- **Permissions:** Run as administrator if needed
- **Antivirus:** Add LESAVOT folder to antivirus exceptions

---

## 🔒 Security Features

### **All Versions Include:**
- ✅ **Offline Operation** - No internet required for core functions
- ✅ **Local Processing** - All encryption done on your device
- ✅ **No Data Collection** - Your files never leave your computer
- ✅ **Military-Grade Encryption** - AES-256 equivalent security
- ✅ **Password Protection** - Secure your hidden messages

### **Desktop App Additional Security:**
- ✅ **Enhanced File System Access**
- ✅ **Secure Memory Management**
- ✅ **Native OS Integration**
- ✅ **Encrypted Local Storage**

---

## 🎯 Quick Test

### **Test the Application:**
1. **Launch LESAVOT** using any method above
2. **Go to Text Steganography**
3. **Enter test message:** "Hello LESAVOT"
4. **Set password:** "test123"
5. **Click Encrypt**
6. **Copy the result and try to decrypt it**

### **Expected Result:**
- Encryption should produce encoded text
- Decryption with correct password should return "Hello LESAVOT"
- Wrong password should show error message

---

## 📞 Support

### **If You Need Help:**
- **GitHub Issues:** https://github.com/Bechi-cyber/FINAL-LESAVOT/issues
- **Email:** <EMAIL>
- **Documentation:** Check THESIS.md for detailed technical information

### **Common Solutions:**
- **Restart the application**
- **Clear browser cache**
- **Update Node.js to latest version**
- **Disable antivirus temporarily during installation**

---

## 🎉 Success Indicators

### **Application is Working When:**
- ✅ Welcome page loads with LESAVOT logo
- ✅ Snowy raindrop animations are visible
- ✅ Authentication page accepts sign-up/sign-in
- ✅ All three steganography modes are accessible
- ✅ Encryption/decryption functions work correctly

---

## 🔄 Updates

### **To Update LESAVOT:**
1. **Download latest version** from GitHub
2. **Replace old files** with new ones
3. **For desktop app:** Run `npm install` again
4. **Clear browser cache** if using web version

---

**🛡️ LESAVOT - THE MORE YOU LOOK, THE LESS YOU SEE**

*Secure. Offline. Professional.*
