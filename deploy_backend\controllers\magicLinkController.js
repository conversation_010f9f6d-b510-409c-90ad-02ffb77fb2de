/**
 * Magic Link Authentication Controller
 * 
 * Handles Magic Link authentication endpoints for the LESAVOT platform.
 * Provides email-only authentication using JWT magic links.
 */

const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const magicLinkService = require('../services/magicLinkService');
const MagicLinkUser = require('../models/MagicLinkUser');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errorHandler');

// Rate limiting for magic link requests
const magicLinkLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // 3 magic link requests per 15 minutes per IP
  message: {
    status: 'error',
    message: 'Too many magic link requests. Please try again in 15 minutes.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Rate limiting for verification attempts
const verifyLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 verification attempts per 15 minutes per IP
  message: {
    status: 'error',
    message: 'Too many verification attempts. Please try again in 15 minutes.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Request Magic Link for Signup
 */
const requestSignupMagicLink = [
  magicLinkLimiter,
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  async (req, res, next) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email } = req.body;

      logger.info(`OTP signup requested for ${email}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Generate OTP for signup
      const result = await magicLinkService.generateMagicLink(email, true);

      res.status(200).json({
        status: 'success',
        message: 'Verification code sent to your email. Please check your inbox and enter the code to complete signup.',
        data: {
          email,
          expiresAt: result.expiresAt
        }
      });

    } catch (error) {
      logger.error('Signup magic link request failed:', {
        error: error.message,
        stack: error.stack,
        email: req.body.email,
        ip: req.ip
      });

      if (error.message.includes('already exists')) {
        return res.status(409).json({
          status: 'error',
          message: 'An account with this email already exists. Try signing in instead.'
        });
      }

      // Return the actual error for debugging
      return res.status(500).json({
        status: 'error',
        message: 'Failed to send magic link. Please try again.',
        debug: error.message,
        stack: error.stack
      });
    }
  }
];

/**
 * Request Magic Link for Signin
 */
const requestSigninMagicLink = [
  magicLinkLimiter,
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  async (req, res, next) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email } = req.body;

      logger.info(`OTP signin requested for ${email}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Generate OTP for signin
      const result = await magicLinkService.generateMagicLink(email, false);

      res.status(200).json({
        status: 'success',
        message: 'Verification code sent to your email. Please check your inbox and enter the code to sign in.',
        data: {
          email,
          expiresAt: result.expiresAt
        }
      });

    } catch (error) {
      logger.error('Signin magic link request failed:', {
        error: error.message,
        stack: error.stack,
        email: req.body.email,
        ip: req.ip
      });

      if (error.message.includes('No account found')) {
        return res.status(404).json({
          status: 'error',
          message: 'No account found with this email. Please sign up first.'
        });
      }

      // Return the actual error for debugging
      return res.status(500).json({
        status: 'error',
        message: 'Failed to send magic link. Please try again.',
        debug: error.message,
        stack: error.stack
      });
    }
  }
];

/**
 * Verify Magic Link and Authenticate User
 */
const verifyMagicLink = [
  verifyLimiter,
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('OTP must be a 6-digit number'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  async (req, res, next) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { otp, email } = req.body;

      logger.info(`OTP verification attempted for ${email}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Verify OTP
      logger.info('About to call magicLinkService.verifyOTP');
      const result = await magicLinkService.verifyOTP(email, otp);
      logger.info('magicLinkService.verifyOTP completed successfully');

      logger.info(`OTP verification successful for ${email}`, {
        userId: result.user.id,
        type: result.type,
        ip: req.ip
      });

      res.status(200).json({
        status: 'success',
        message: result.message,
        user: result.user,
        sessionToken: result.sessionToken,
        type: result.type
      });

    } catch (error) {
      logger.error('Magic link verification failed:', {
        error: error.message,
        email: req.body.email,
        ip: req.ip
      });

      if (error.message.includes('Invalid or expired')) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid or expired magic link. Please request a new one.'
        });
      }

      if (error.message.includes('Account is temporarily locked')) {
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked due to too many failed attempts. Please try again later.'
        });
      }

      // Return the actual error for debugging
      return res.status(401).json({
        status: 'error',
        message: 'Authentication failed. Please try again.',
        debug: error.message,
        stack: error.stack
      });
    }
  }
];

/**
 * Get Current User Profile
 */
const getCurrentUser = async (req, res, next) => {
  try {
    // User is already attached to req by auth middleware
    const user = await MagicLinkUser.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        user: user.toJSON()
      }
    });

  } catch (error) {
    logger.error('Get current user failed:', {
      error: error.message,
      userId: req.user?.userId
    });

    next(new AppError('Failed to get user profile', 500));
  }
};

/**
 * Update User Profile
 */
const updateProfile = [
  body('displayName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  
  async (req, res, next) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const user = await MagicLinkUser.findById(req.user.userId);
      
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Update user profile
      if (req.body.displayName) {
        user.displayName = req.body.displayName;
      }

      await user.save();

      logger.info(`User profile updated for ${user.email}`, {
        userId: user.id,
        changes: req.body
      });

      res.status(200).json({
        status: 'success',
        message: 'Profile updated successfully',
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      logger.error('Update profile failed:', {
        error: error.message,
        userId: req.user?.userId
      });

      next(new AppError('Failed to update profile', 500));
    }
  }
];

/**
 * Logout User (invalidate session)
 */
const logout = async (req, res, next) => {
  try {
    // In a stateless JWT system, logout is handled client-side
    // But we can log the logout event
    logger.info(`User logged out`, {
      userId: req.user?.userId,
      email: req.user?.email,
      ip: req.ip
    });

    res.status(200).json({
      status: 'success',
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout failed:', {
      error: error.message,
      userId: req.user?.userId
    });

    next(new AppError('Logout failed', 500));
  }
};

module.exports = {
  requestSignupMagicLink,
  requestSigninMagicLink,
  verifyMagicLink,
  getCurrentUser,
  updateProfile,
  logout
};
