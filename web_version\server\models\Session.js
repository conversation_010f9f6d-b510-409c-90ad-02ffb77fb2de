/**
 * Session Model - Simplified Authentication Sessions
 *
 * Handles authentication sessions without OTP functionality
 */

const crypto = require('crypto');
const database = require('../utils/database');
const logger = require('../utils/logger');

class Session {
  constructor(sessionData = {}) {
    this.id = sessionData.id || null;
    this.sessionId = sessionData.session_id || sessionData.sessionId || this.generateSessionId();
    this.userId = sessionData.user_id || sessionData.userId || null;
    this.username = sessionData.username;
    this.sessionType = 'auth'; // Only auth sessions now
    this.isVerified = true; // Always verified in simplified auth
    this.expiresAt = sessionData.expires_at || sessionData.expiresAt;
    this.createdAt = sessionData.created_at || sessionData.createdAt || null;
  }

  /**
   * Generate unique session ID
   */
  generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Save session to database (simplified)
   */
  async save() {
    try {
      if (this.id) {
        // Update existing session
        const result = await database.query(`
          UPDATE sessions
          SET session_id = $1, user_id = $2, username = $3, session_type = $4,
              is_verified = $5, expires_at = $6
          WHERE id = $7
          RETURNING id, session_id, created_at
        `, [
          this.sessionId, this.userId || null, this.username, this.sessionType,
          this.isVerified, this.expiresAt, this.id
        ]);

        return result.rows.length > 0;
      } else {
        // Create new session
        const result = await database.query(`
          INSERT INTO sessions (session_id, user_id, username, session_type, is_verified, expires_at)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING id, session_id, created_at
        `, [
          this.sessionId, this.userId || null, this.username, this.sessionType,
          this.isVerified, this.expiresAt
        ]);

        if (result.rows.length > 0) {
          const session = result.rows[0];
          this.id = session.id;
          this.createdAt = session.created_at;
          return true;
        }
        return false;
      }
    } catch (error) {
      logger.error('Error saving session:', error);
      // Don't throw error - sessions are not critical for simplified auth
      logger.warn('Session save failed, continuing without session');
      return false;
    }
  }

  /**
   * Find session by session ID (simplified)
   */
  static async findBySessionId(sessionId) {
    try {
      const result = await database.query(`
        SELECT id, session_id, user_id, username, session_type,
               is_verified, expires_at, created_at
        FROM sessions
        WHERE session_id = $1 AND session_type = 'auth' AND expires_at > CURRENT_TIMESTAMP
      `, [sessionId]);

      return result.rows.length > 0 ? new Session(result.rows[0]) : null;
    } catch (error) {
      logger.error('Error finding session by ID:', error);
      // Don't throw error - return null for simplified auth
      return null;
    }
  }

  /**
   * Find active sessions by username (simplified)
   */
  static async findByUsername(username) {
    try {
      const query = `
        SELECT id, session_id, user_id, username, session_type,
               is_verified, expires_at, created_at
        FROM sessions
        WHERE username = $1 AND session_type = 'auth' AND expires_at > CURRENT_TIMESTAMP
        ORDER BY created_at DESC
      `;

      const result = await database.query(query, [username]);
      return result.rows.map(sessionData => new Session(sessionData));
    } catch (error) {
      logger.error('Error finding sessions by username:', error);
      return []; // Return empty array for simplified auth
    }
  }

  // OTP-related methods removed for simplified authentication
      return []; // Return empty array for simplified auth
    }
  }

  /**
   * Create authentication session (simplified)
   */
  static async createAuthSession(userId, username, expiryHours = 24) {
    try {
      const expiresAt = new Date(Date.now() + (expiryHours * 60 * 60 * 1000));

      const session = new Session({
        userId,
        username,
        sessionType: 'auth',
        isVerified: true,
        expiresAt
      });

      await session.save();
      return session;
    } catch (error) {
      logger.error('Error creating auth session:', error);
      // Don't throw error - sessions are not critical for simplified auth
      logger.warn('Auth session creation failed, continuing without session');
      return null;
    }
  }

  /**
   * Invalidate session (delete it)
   */
  async invalidate() {
    try {
      const result = await database.query(`
        DELETE FROM sessions WHERE id = $1
      `, [this.id]);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error invalidating session:', error);
      return false; // Don't throw error for simplified auth
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  static async invalidateAllUserSessions(username) {
    try {
      const result = await database.query(`
        DELETE FROM sessions WHERE username = $1
      `, [username]);

      return result.rowCount;
    } catch (error) {
      logger.error('Error invalidating all user sessions:', error);
      return 0; // Don't throw error for simplified auth
    }
  }

  /**
   * Clean up expired sessions
   */
  static async cleanupExpiredSessions() {
    try {
      const result = await database.query(`
        DELETE FROM sessions WHERE expires_at < CURRENT_TIMESTAMP
      `);

      logger.info(`Cleaned up ${result.rowCount} expired sessions`);
      return result.rowCount;
    } catch (error) {
      logger.error('Error cleaning up expired sessions:', error);
      throw error;
    }
  }

  /**
   * Check if session is valid
   */
  isValid() {
    return this.expiresAt && new Date(this.expiresAt) > new Date();
  }

  /**
   * Extend session expiry
   */
  async extend(additionalHours = 24) {
    try {
      this.expiresAt = new Date(Date.now() + (additionalHours * 60 * 60 * 1000));

      const result = await database.query(`
        UPDATE sessions
        SET expires_at = $1
        WHERE id = $2
      `, [this.expiresAt, this.id]);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error extending session:', error);
      throw error;
    }
  }

  /**
   * Update last activity timestamp
   */
  async updateLastActivity() {
    try {
      // For now, we'll just extend the session expiry as a form of activity update
      // In a more complex system, you might have a separate last_activity column
      await this.extend(24);
      return true;
    } catch (error) {
      logger.error('Error updating last activity:', error);
      throw error;
    }
  }

  /**
   * Get active sessions count for user
   */
  static async getActiveSessionsCount(username) {
    try {
      const result = await database.query(`
        SELECT COUNT(*) as count
        FROM sessions
        WHERE username = $1 AND expires_at > CURRENT_TIMESTAMP
      `, [username]);

      return parseInt(result.rows[0].count);
    } catch (error) {
      logger.error('Error getting active sessions count:', error);
      throw error;
    }
  }

  /**
   * Get session by ID (simplified)
   */
  static async findById(id) {
    try {
      const result = await database.query(`
        SELECT id, session_id, user_id, username, session_type,
               is_verified, expires_at, created_at
        FROM sessions
        WHERE id = $1
      `, [id]);

      return result.rows.length > 0 ? new Session(result.rows[0]) : null;
    } catch (error) {
      logger.error('Error finding session by ID:', error);
      return null; // Don't throw error for simplified auth
    }
  }

  /**
   * Convert to safe object (removes sensitive data)
   */
  toSafeObject() {
    return {
      id: this.id,
      sessionId: this.sessionId,
      userId: this.userId,
      username: this.username,
      sessionType: this.sessionType,
      isVerified: this.isVerified,
      expiresAt: this.expiresAt,
      createdAt: this.createdAt
    };
  }
}

module.exports = Session;
