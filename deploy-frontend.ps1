# Deploy Frontend to GitHub Repository
# Repository: https://github.com/Bechi-cyber/LASAVOT

Write-Host "🚀 LESAVOT Frontend Deployment Script" -ForegroundColor Yellow
Write-Host "Repository: https://github.com/Bechi-cyber/LASAVOT" -ForegroundColor Cyan
Write-Host "Source: web_version directory" -ForegroundColor Cyan

# Set variables
$frontendRepo = "https://github.com/Bechi-cyber/LASAVOT.git"
$sourceDir = "web_version"
$tempDir = "temp_frontend_deploy"

# Function to display colored messages
function Write-ColoredOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    Write-Host $Message -ForegroundColor $ForegroundColor
}

try {
    # Check if Git is available
    Write-ColoredOutput "🔍 Checking Git installation..." "Cyan"
    $gitVersion = git --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-ColoredOutput "❌ Git is not installed or not in PATH" "Red"
        exit 1
    }
    Write-ColoredOutput "✅ Git found: $gitVersion" "Green"

    # Check if source directory exists
    if (-not (Test-Path $sourceDir)) {
        Write-ColoredOutput "❌ Source directory '$sourceDir' not found" "Red"
        exit 1
    }
    Write-ColoredOutput "✅ Source directory found: $sourceDir" "Green"

    # Clean up any existing temp directory
    if (Test-Path $tempDir) {
        Write-ColoredOutput "🧹 Cleaning up existing temp directory..." "Yellow"
        Remove-Item -Recurse -Force $tempDir
    }

    # Create temp directory and navigate to it
    Write-ColoredOutput "📁 Creating temporary deployment directory..." "Cyan"
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    Set-Location $tempDir

    # Clone the repository
    Write-ColoredOutput "📥 Cloning frontend repository..." "Cyan"
    git clone $frontendRepo . 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-ColoredOutput "❌ Failed to clone repository. Initializing new repository..." "Yellow"
        git init
        git remote add origin $frontendRepo
    }

    # Copy frontend files
    Write-ColoredOutput "📋 Copying frontend files..." "Cyan"
    $sourceFullPath = Join-Path ".." $sourceDir
    Copy-Item -Path "$sourceFullPath\*" -Destination "." -Recurse -Force

    # Create/update README for frontend
    Write-ColoredOutput "📝 Creating README.md..." "Cyan"
    $readmeContent = @"
# LESAVOT - Advanced Multimodal Steganography Platform (Frontend)

## 🔐 Secure Steganography Web Application

LESAVOT is a cutting-edge web application for advanced steganography operations, supporting text, image, and audio steganography with enterprise-grade security features.

## ✨ Features

### 🔒 Authentication System
- Secure user registration and login
- JWT-based session management
- Password strength validation
- Eye icon password visibility toggle
- Clean, user-friendly interface

### 🛡️ Steganography Capabilities
- **Text Steganography**: Hide messages in plain text
- **Image Steganography**: Embed data in images using LSB techniques
- **Audio Steganography**: Conceal information in audio files
- **Real-time Processing**: Instant encode/decode operations

### 🎨 User Interface
- Modern, responsive design
- Cybersecurity-themed aesthetics
- Intuitive navigation
- Mobile-friendly layout
- Accessibility features

### 🔧 Technical Features
- Progressive Web App (PWA) support
- Offline functionality
- Service worker caching
- Cross-browser compatibility
- Security headers and CORS protection

## 🚀 Live Demo

- **Production**: [https://lesavot.vercel.app](https://lesavot.vercel.app)
- **Alternative**: [https://lasavot.onrender.com](https://lasavot.onrender.com)

## 🛠️ Local Development

### Prerequisites
- Modern web browser
- Local web server (Python, Node.js, or any HTTP server)

### Quick Start
1. Clone this repository
2. Start a local web server:
   ```bash
   # Using Python
   python -m http.server 8080
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8080
   ```
3. Open http://localhost:8080 in your browser

### Project Structure
```
/
├── auth.html              # Authentication page
├── text_stego.html        # Text steganography
├── image_stego.html       # Image steganography  
├── audio_stego.html       # Audio steganography
├── profile.html           # User profile
├── history.html           # Operation history
├── simple-auth.js         # Authentication logic
├── config.js              # Configuration
├── styles.css             # Main styles
└── manifest.json          # PWA manifest
```

## 🔗 Backend Integration

This frontend connects to the LESAVOT backend API:
- **Repository**: [https://github.com/Bechi-cyber/LASAVOT-Backend](https://github.com/Bechi-cyber/LASAVOT-Backend)
- **Production API**: https://lasavot-backend.onrender.com

## 🔒 Security Features

- Content Security Policy (CSP)
- XSS Protection
- CSRF Protection
- Secure Headers
- Input Validation
- Session Management

## 📱 Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and support:
- Create an issue on GitHub
- Check the documentation
- Review the deployment guides

---

**Last Updated**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**Version**: 1.0.0
**Status**: ✅ Production Ready
"@

    $readmeContent | Out-File -FilePath "README.md" -Encoding UTF8

    # Add all files
    Write-ColoredOutput "➕ Adding files to Git..." "Cyan"
    git add .

    # Check if there are changes to commit
    $status = git status --porcelain
    if (-not $status) {
        Write-ColoredOutput "ℹ️ No changes to commit" "Yellow"
    } else {
        # Commit changes
        $commitMessage = "Frontend deployment - Authentication system updates and backend integration - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        Write-ColoredOutput "💾 Committing changes..." "Cyan"
        git commit -m $commitMessage

        # Push to repository
        Write-ColoredOutput "🚀 Pushing to GitHub..." "Cyan"
        git push -u origin main 2>$null
        if ($LASTEXITCODE -ne 0) {
            # Try master branch if main fails
            git push -u origin master 2>$null
            if ($LASTEXITCODE -ne 0) {
                Write-ColoredOutput "❌ Failed to push to repository" "Red"
                exit 1
            }
        }
        Write-ColoredOutput "✅ Successfully pushed to GitHub!" "Green"
    }

    # Return to original directory
    Set-Location ".."

    # Clean up temp directory
    Write-ColoredOutput "🧹 Cleaning up..." "Yellow"
    Remove-Item -Recurse -Force $tempDir

    Write-ColoredOutput "`n🎉 Frontend deployment completed successfully!" "Green"
    Write-ColoredOutput "Repository: https://github.com/Bechi-cyber/LASAVOT" "Cyan"
    Write-ColoredOutput "Frontend deployed and ready!" "Green"

} catch {
    Write-ColoredOutput "❌ Error during deployment: $_" "Red"
    
    # Clean up on error
    Set-Location ".." -ErrorAction SilentlyContinue
    if (Test-Path $tempDir) {
        Remove-Item -Recurse -Force $tempDir -ErrorAction SilentlyContinue
    }
    
    exit 1
}

Write-ColoredOutput "`nPress any key to exit..." "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
