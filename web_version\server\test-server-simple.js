// Simple server test to verify basic functionality
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/test', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Server is working!',
    timestamp: new Date().toISOString()
  });
});

// Test auth route
app.post('/api/auth/test', (req, res) => {
  console.log('Test auth route hit:', req.body);
  res.json({
    success: true,
    message: 'Auth route is working!',
    received: req.body
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`✅ Test server running on port ${PORT}`);
  console.log(`   Test URL: http://localhost:${PORT}/api/test`);
  console.log(`   Auth test: http://localhost:${PORT}/api/auth/test`);
});
