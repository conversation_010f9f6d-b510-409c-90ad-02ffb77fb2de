services:
  - type: web
    name: lesavot-backend
    env: node
    buildCommand: npm install
    startCommand: npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DATABASE_URL
        fromDatabase:
          name: lesavot-postgres
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRES_IN
        value: 7d
      - key: JWT_REFRESH_EXPIRES_IN
        value: 30d
      - key: BCRYPT_ROUNDS
        value: 12
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 100
      - key: CORS_ORIGIN
        value: https://lasavot.onrender.com
      - key: ALLOWED_ORIGINS
        value: http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://127.0.0.1:8080,https://lasavot.onrender.com
      - key: EMAIL_USER
        value: ""
      - key: EMAIL_PASSWORD
        value: ""
      - key: EMAIL_HOST
        value: smtp.gmail.com
      - key: EMAIL_PORT
        value: 587

databases:
  - name: lesavot-postgres
    databaseName: lesavotdb
    user: bechi
