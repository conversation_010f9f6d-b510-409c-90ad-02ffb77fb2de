const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

// Simple OTP storage (in-memory for testing)
const otpStorage = new Map();
const userStorage = new Map();

// Generate 6-digit OTP
function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

// Generate session token (simple for testing)
function generateSessionToken(email) {
    return Buffer.from(`${email}:${Date.now()}`).toString('base64');
}

// OTP Routes
app.post('/api/auth/signup/request', (req, res) => {
    try {
        const { email } = req.body;
        
        if (!email || !email.includes('@')) {
            return res.status(400).json({
                status: 'error',
                message: 'Valid email is required'
            });
        }

        // Check if user already exists
        if (userStorage.has(email)) {
            return res.status(400).json({
                status: 'error',
                message: 'User already exists. Please sign in instead.'
            });
        }

        const otp = generateOTP();
        const expiresAt = Date.now() + (10 * 60 * 1000); // 10 minutes

        otpStorage.set(email, {
            otp,
            type: 'signup',
            expiresAt,
            attempts: 0
        });

        console.log(`📧 OTP for ${email}: ${otp} (Signup)`);

        res.json({
            status: 'success',
            message: 'OTP sent to your email address',
            expiresAt
        });
    } catch (error) {
        console.error('Signup OTP error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to send OTP'
        });
    }
});

app.post('/api/auth/signin/request', (req, res) => {
    try {
        const { email } = req.body;
        
        if (!email || !email.includes('@')) {
            return res.status(400).json({
                status: 'error',
                message: 'Valid email is required'
            });
        }

        // Check if user exists
        if (!userStorage.has(email)) {
            return res.status(404).json({
                status: 'error',
                message: 'User not found. Please sign up first.'
            });
        }

        const otp = generateOTP();
        const expiresAt = Date.now() + (10 * 60 * 1000); // 10 minutes

        otpStorage.set(email, {
            otp,
            type: 'signin',
            expiresAt,
            attempts: 0
        });

        console.log(`📧 OTP for ${email}: ${otp} (Signin)`);

        res.json({
            status: 'success',
            message: 'OTP sent to your email address',
            expiresAt
        });
    } catch (error) {
        console.error('Signin OTP error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to send OTP'
        });
    }
});

app.post('/api/auth/verify', (req, res) => {
    try {
        const { email, otp } = req.body;

        if (!email || !otp) {
            return res.status(400).json({
                status: 'error',
                message: 'Email and OTP are required'
            });
        }

        const otpData = otpStorage.get(email);
        
        if (!otpData) {
            return res.status(400).json({
                status: 'error',
                message: 'OTP not found or expired'
            });
        }

        // Check expiry
        if (Date.now() > otpData.expiresAt) {
            otpStorage.delete(email);
            return res.status(400).json({
                status: 'error',
                message: 'OTP has expired'
            });
        }

        // Check attempts
        if (otpData.attempts >= 3) {
            otpStorage.delete(email);
            return res.status(400).json({
                status: 'error',
                message: 'Maximum verification attempts exceeded'
            });
        }

        // Verify OTP
        if (otpData.otp !== otp) {
            otpData.attempts++;
            otpStorage.set(email, otpData);
            return res.status(400).json({
                status: 'error',
                message: `Invalid OTP. ${3 - otpData.attempts} attempts remaining`
            });
        }

        // OTP is valid
        otpStorage.delete(email);

        let user;
        if (otpData.type === 'signup') {
            // Create new user
            user = {
                id: Date.now(),
                email,
                username: email.split('@')[0],
                displayName: email.split('@')[0],
                isVerified: true,
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString()
            };
            userStorage.set(email, user);
        } else {
            // Get existing user
            user = userStorage.get(email);
            user.lastLogin = new Date().toISOString();
            userStorage.set(email, user);
        }

        const sessionToken = generateSessionToken(email);

        console.log(`✅ OTP verified for ${email} (${otpData.type})`);

        res.json({
            status: 'success',
            message: `${otpData.type === 'signup' ? 'Signup' : 'Signin'} successful`,
            user,
            sessionToken,
            type: otpData.type
        });
    } catch (error) {
        console.error('OTP verification error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Verification failed'
        });
    }
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'success',
        message: 'OTP Authentication Server is running',
        timestamp: new Date().toISOString(),
        users: userStorage.size,
        activeOTPs: otpStorage.size
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`✅ OTP Authentication Server running on port ${PORT}`);
    console.log(`✅ Frontend: http://localhost:${PORT}`);
    console.log(`✅ API: http://localhost:${PORT}/api`);
    console.log(`✅ Health: http://localhost:${PORT}/api/health`);
    console.log('📧 OTP codes will be logged to console (check terminal for codes)');
});

module.exports = app;
