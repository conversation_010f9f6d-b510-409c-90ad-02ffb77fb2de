<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Extracted Message</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .nav {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav a {
            display: inline-block;
            background-color: #555;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin: 0 5px;
        }
        .nav a:hover {
            background-color: #333;
        }
        .result-container {
            margin-top: 30px;
        }
        .audio-container {
            margin-top: 20px;
            text-align: center;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .message-box {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Message Extraction Result</h1>
    
    <div class="nav">
        <a href="/">Home</a>
        <a href="/image">Image</a>
        <a href="/text">Text</a>
        <a href="/audio">Audio</a>
    </div>
    
    <div class="result-container">
        <div class="audio-container">
            <h2>Uploaded Audio</h2>
            <audio controls>
                <source src="{{ url_for('download', filename=filename) }}" type="audio/wav">
                Your browser does not support the audio element.
            </audio>
        </div>
        
        <div class="message-box">
            <h3>Extracted Message:</h3>
            <p>{{ message }}</p>
        </div>
    </div>
</body>
</html>
