<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT | Audio Steganography</title>
    <link rel="stylesheet" href="text_stego.css">
    <link rel="stylesheet" href="audio_stego.css">
    <link rel="stylesheet" href="cybersecurity_elements.css">
    <link rel="stylesheet" href="snowy_raindrops.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <header>
            <div class="snowflake-container">
                <!-- Single raindrops -->
                <div class="snowflake snow-1">│</div>
                <div class="snowflake snow-2">│</div>
                <div class="snowflake snow-3">│</div>
                <div class="snowflake snow-4">│</div>
                <div class="snowflake snow-5">│</div>
                <div class="snowflake snow-6">│</div>
                <div class="snowflake snow-7">│</div>
                <div class="snowflake snow-8">│</div>
                <div class="snowflake snow-9">│</div>
                <div class="snowflake snow-10">│</div>
                <div class="snowflake snow-11">│</div>
                <div class="snowflake snow-12">│</div>
                <div class="snowflake snow-13">│</div>
                <div class="snowflake snow-14">│</div>
                <div class="snowflake snow-15">│</div>
                <div class="snowflake snow-16">│</div>
                <div class="snowflake snow-17">│</div>
                <div class="snowflake snow-18">│</div>
                <div class="snowflake snow-19">│</div>
                <div class="snowflake snow-20">│</div>

                <!-- Raindrop lines -->
                <div class="raindrop-line rain-line-1">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-2">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-3">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-4">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-5">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-6">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-7">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-8">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-9">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-10">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-11">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-12">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-13">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-14">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-15">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-16">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-17">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-18">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-19">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-20">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-21">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-22">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-23">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-24">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-25">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-26">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-27">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-28">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-29">│<br>│<br>│</div>
                <div class="raindrop-line rain-line-30">│<br>│<br>│</div>
            </div>
            <div class="header-container">

                <div class="logo">
                    <i class="fas fa-shield-alt logo-icon"></i>
                    <span class="logo-text">LESAVOT</span>
                </div>
                <div class="subtitle">THE MORE YOU LOOK, THE LESS YOU SEE</div>
            </div>
            <div class="user-info" id="userInfo">
                <span id="welcomeMessage">Welcome</span>
                <div class="user-actions">
                    <button type="button" class="btn-icon" title="Profile">
                        <i class="fas fa-user"></i>
                    </button>
                    <a href="auth.html" class="btn-icon active-logout" title="Sign Out" id="signOutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </header>

        <div class="tab-navigation">
            <button type="button" class="tab-btn" onclick="window.location.href='text_stego.html'">
                <i class="fas fa-font"></i>
                <span>Text</span>
            </button>
            <button type="button" class="tab-btn" onclick="window.location.href='image_stego.html'">
                <i class="fas fa-image"></i>
                <span>Image</span>
            </button>
            <button type="button" class="tab-btn active">
                <i class="fas fa-volume-up"></i>
                <span>Audio</span>
            </button>
        </div>

        <div id="notificationArea"></div>

        <main>
            <!-- Main Content Area -->
            <div class="content-container">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-volume-up"></i>
                        <h2>Audio Steganography</h2>
                    </div>

                    <div class="card-body">
                        <div class="mode-selector">
                            <label class="radio-container">
                                <input type="radio" name="audioMode" value="encrypt" checked>
                                <span class="radio-label">Encrypt</span>
                            </label>
                            <label class="radio-container">
                                <input type="radio" name="audioMode" value="decrypt">
                                <span class="radio-label">Decrypt</span>
                            </label>
                        </div>

                        <div id="audioEncrypt" class="mode-content">
                            <div class="form-group">
                                <label for="audioUpload">Cover Audio:</label>
                                <div class="file-upload-container">
                                    <input type="file" id="audioUpload" accept="audio/*" class="file-input">
                                    <label for="audioUpload" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i> Choose Audio
                                    </label>
                                    <span id="audioFileName" class="file-name">No file chosen</span>
                                </div>
                                <div id="audioPlayerContainer" class="audio-player-container" style="display: none;">
                                    <audio id="audioPlayer" controls></audio>
                                    <div id="audioWaveform" class="waveform-container"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="audioMessage">Secret Message:</label>
                                <textarea id="audioMessage" placeholder=""></textarea>
                            </div>

                            <div class="form-group">
                                <label for="audioPassword">Password:</label>
                                <div class="password-input">
                                    <input type="password" id="audioPassword" placeholder="">
                                </div>
                            </div>

                            <button type="button" id="audioEncryptBtn" class="btn btn-primary">
                                <i class="fas fa-lock"></i> Encrypt
                            </button>

                            <div id="audioOutputContainer" class="form-group output-container" style="display: none;">
                                <label>Output Audio:</label>
                                <div class="audio-result-container">
                                    <audio id="audioOutput" controls></audio>
                                    <div id="audioOutputWaveform" class="waveform-container"></div>
                                </div>
                                <div class="output-actions">
                                    <button type="button" id="audioSaveBtn" class="btn btn-outline">
                                        <i class="fas fa-download"></i> Save Audio
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="audioDecrypt" class="mode-content" style="display: none;">
                            <div class="form-group">
                                <label for="audioDecryptUpload">Audio with Hidden Message:</label>
                                <div class="file-upload-container">
                                    <input type="file" id="audioDecryptUpload" accept="audio/*" class="file-input">
                                    <label for="audioDecryptUpload" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i> Choose Audio
                                    </label>
                                    <span id="audioDecryptFileName" class="file-name">No file chosen</span>
                                </div>
                                <div id="audioDecryptPlayerContainer" class="audio-player-container" style="display: none;">
                                    <audio id="audioDecryptPlayer" controls></audio>
                                    <div id="audioDecryptWaveform" class="waveform-container"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="audioDecryptPassword">Password:</label>
                                <div class="password-input">
                                    <input type="password" id="audioDecryptPassword" placeholder="">
                                </div>
                            </div>

                            <button type="button" id="audioDecryptBtn" class="btn btn-primary">
                                <i class="fas fa-unlock"></i> Decrypt
                            </button>

                            <div class="form-group">
                                <label for="audioExtractedMessage">Decrypted Message:</label>
                                <textarea id="audioExtractedMessage" readonly placeholder=""></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="user-auth.js"></script>
    <script src="audio_stego.js"></script>
</body>
</html>
