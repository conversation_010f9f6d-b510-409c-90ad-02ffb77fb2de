# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_KEY=your_service_key_here
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_DB_PASSWORD=your_database_password_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
JWT_COOKIE_EXPIRES_IN=7

# Server Configuration
PORT=3000
NODE_ENV=development
API_VERSION=v1
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5500,http://127.0.0.1:5500,https://your-production-domain.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,Accept,Origin

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
ENABLE_HELMET=true
ENABLE_XSS_PROTECTION=true
ENABLE_CONTENT_SECURITY_POLICY=true

# Error Handling
SHOW_STACK_TRACES=false
LOG_ERRORS=true

# Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_MONITORING_INTERVAL_MS=60000
