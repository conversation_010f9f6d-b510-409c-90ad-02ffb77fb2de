# LESAVOT Enhanced JWT Configuration Example
# Copy this file to .env and configure your values

# Database Configuration
DATABASE_URL=your_database_url_here
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Enhanced JWT Configuration
# Choose algorithm: HS256 (symmetric) or RS256 (asymmetric - recommended)
JWT_ALGORITHM=RS256

# For HS256 (symmetric) - use strong secrets
JWT_SECRET=your_very_strong_jwt_secret_here_minimum_256_bits
JWT_REFRESH_SECRET=your_very_strong_refresh_secret_here_minimum_256_bits

# For RS256 (asymmetric) - use RSA key pairs
# Generate with: openssl genrsa -out private.pem 2048
# Extract public: openssl rsa -in private.pem -pubout -out public.pem
JWT_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\nYour private key here\n-----END RSA PRIVATE KEY-----"
JWT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nYour public key here\n-----END PUBLIC KEY-----"

# Optional: Separate keys for refresh tokens
JWT_REFRESH_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\nYour refresh private key here\n-----END RSA PRIVATE KEY-----"
JWT_REFRESH_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nYour refresh public key here\n-----END PUBLIC KEY-----"

# Token Expiration (shorter for better security)
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# JWT Metadata
JWT_ISSUER=lesavot-platform
JWT_AUDIENCE=lesavot-users
JWT_KEY_ID=lesavot-key-1

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# OTP Configuration
OTP_EXPIRY_MINUTES=10
MAX_OTP_ATTEMPTS=3

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
