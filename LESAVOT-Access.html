<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT - Secure Steganography Platform</title>
    <style>
        :root {
            --primary-color: #1a3a5f;
            --secondary-color: #3498db;
            --accent-color: #2ecc71;
            --text-color: #333;
            --light-bg: #f5f7fa;
            --dark-bg: #2c3e50;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            margin: 0;
            padding: 0;
            background-color: var(--light-bg);
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        
        .tagline {
            font-style: italic;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        .main-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 40px 0;
        }
        
        .qr-section {
            flex: 1;
            min-width: 300px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 30px;
            text-align: center;
            margin: 10px;
        }
        
        .info-section {
            flex: 1;
            min-width: 300px;
            margin: 10px;
        }
        
        .qr-container {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            display: inline-block;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .qr-code {
            margin: 0 auto;
        }
        
        .url-display {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 20px 0;
        }
        
        .button {
            display: inline-block;
            background-color: var(--secondary-color);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
            margin: 10px 0;
        }
        
        .button:hover {
            background-color: var(--primary-color);
        }
        
        .feature-list {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .feature-list h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 10px;
        }
        
        .feature-list ul {
            padding-left: 20px;
        }
        
        .feature-list li {
            margin-bottom: 10px;
        }
        
        .instructions {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .instructions h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 10px;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 15px;
        }
        
        footer {
            background-color: var(--dark-bg);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .qr-section, .info-section {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>LESAVOT</h1>
            <p class="tagline">The More You Look, The Less You See</p>
        </div>
    </header>
    
    <div class="container">
        <div class="main-content">
            <div class="qr-section">
                <h2>Scan to Access</h2>
                <div class="qr-container">
                    <img class="qr-code" src="https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=https://bechi-cyber.github.io/FINAL-LESAVOT/" alt="QR Code for LESAVOT">
                </div>
                <p>Scan this QR code with your smartphone camera to access the LESAVOT application</p>
                <div class="url-display">https://bechi-cyber.github.io/FINAL-LESAVOT/</div>
                <a href="https://bechi-cyber.github.io/FINAL-LESAVOT/" class="button" target="_blank">Open Application</a>
            </div>
            
            <div class="info-section">
                <div class="feature-list">
                    <h2>Features</h2>
                    <ul>
                        <li><strong>Text Steganography</strong> - Hide messages within innocent-looking text</li>
                        <li><strong>Image Steganography</strong> - Conceal data within images without visible changes</li>
                        <li><strong>Audio Steganography</strong> - Embed secret messages in audio files</li>
                        <li><strong>Password Protection</strong> - Secure your hidden data with passwords</li>
                        <li><strong>User Accounts</strong> - Create an account to track your steganography history</li>
                        <li><strong>Cross-Platform</strong> - Works on desktop and mobile devices</li>
                    </ul>
                </div>
                
                <div class="instructions">
                    <h2>How to Access</h2>
                    <ol>
                        <li>Open your smartphone camera app and point it at the QR code</li>
                        <li>Tap on the notification that appears</li>
                        <li>The LESAVOT application will open in your browser</li>
                        <li>Create an account or sign in to start using the platform</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <div class="container">
            <p>&copy; 2025 LESAVOT - Multimodal Steganography Platform</p>
        </div>
    </footer>
</body>
</html>
