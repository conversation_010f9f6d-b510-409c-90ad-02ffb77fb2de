<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced JWT Testing - LESAVOT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .token-display {
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Enhanced JWT Security Testing</h1>
        <p style="text-align: center; color: #666;">Test the enhanced JWT authentication features with improved security</p>

        <!-- Login Test Section -->
        <div class="test-section">
            <h3>1. Enhanced Login Test</h3>
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="testuser" placeholder="Enter username">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="password123" placeholder="Enter password">
            </div>
            <button onclick="testLogin()">Test Enhanced Login</button>
            <button onclick="testRegister()">Test Registration</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- Token Analysis Section -->
        <div class="test-section">
            <h3>2. Token Security Analysis</h3>
            <div class="form-group">
                <label for="tokenInput">JWT Token:</label>
                <textarea id="tokenInput" rows="3" placeholder="Paste JWT token here for analysis"></textarea>
            </div>
            <button onclick="analyzeToken()">Analyze Token Security</button>
            <button onclick="decodeToken()">Decode Token</button>
            <div id="tokenResult" class="result" style="display: none;"></div>
        </div>

        <!-- Token Validation Test -->
        <div class="test-section">
            <h3>3. Enhanced Validation Test</h3>
            <button onclick="testTokenValidation()">Test Current Token</button>
            <button onclick="testExpiredToken()">Test Expired Token</button>
            <button onclick="testInvalidToken()">Test Invalid Token</button>
            <div id="validationResult" class="result" style="display: none;"></div>
        </div>

        <!-- Security Features Demo -->
        <div class="test-section">
            <h3>4. Security Features Demo</h3>
            <button onclick="showSecurityFeatures()">Show Security Features</button>
            <button onclick="testFingerprinting()">Test Token Fingerprinting</button>
            <button onclick="testRefreshToken()">Test Token Refresh</button>
            <div id="securityResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1';
        let currentToken = null;
        let currentFingerprint = null;

        // Enhanced Login Test
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                showResult(resultDiv, 'Testing enhanced login...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.token;
                    currentFingerprint = data.fingerprint;
                    
                    // Decode token to show enhanced features
                    const decoded = JSON.parse(atob(data.token.split('.')[1]));
                    
                    const result = `✅ Enhanced Login Successful!
                    
🔑 Token Features:
- Algorithm: ${decoded.alg || 'HS256'}
- Issuer: ${decoded.iss}
- Audience: ${decoded.aud}
- Token ID: ${decoded.jti}
- Fingerprint: ${data.fingerprint ? data.fingerprint.substring(0, 16) + '...' : 'N/A'}
- Expires: ${new Date(decoded.exp * 1000).toLocaleString()}
- Type: ${decoded.type || 'access'}

📊 Security Enhancements:
- Short expiration (15 minutes)
- Unique token ID for tracking
- Token fingerprinting enabled
- Enhanced validation active`;
                    
                    showResult(resultDiv, result, 'success');
                    
                    // Auto-fill token for analysis
                    document.getElementById('tokenInput').value = data.token;
                } else {
                    showResult(resultDiv, `❌ Login Failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ Network Error: ${error.message}`, 'error');
            }
        }

        // Test Registration
        async function testRegister() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                showResult(resultDiv, 'Testing registration...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        username, 
                        password,
                        email: `${username}@test.com`
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult(resultDiv, `✅ Registration Successful! User created: ${data.user?.username}`, 'success');
                } else {
                    showResult(resultDiv, `❌ Registration Failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ Network Error: ${error.message}`, 'error');
            }
        }

        // Analyze Token Security
        function analyzeToken() {
            const token = document.getElementById('tokenInput').value;
            const resultDiv = document.getElementById('tokenResult');
            
            if (!token) {
                showResult(resultDiv, '❌ Please enter a JWT token', 'error');
                return;
            }

            try {
                const parts = token.split('.');
                if (parts.length !== 3) {
                    throw new Error('Invalid JWT format');
                }

                const header = JSON.parse(atob(parts[0]));
                const payload = JSON.parse(atob(parts[1]));
                
                const now = Math.floor(Date.now() / 1000);
                const isExpired = payload.exp && payload.exp < now;
                const timeToExpiry = payload.exp ? payload.exp - now : null;
                
                const analysis = `🔍 JWT Security Analysis:

📋 Header:
${JSON.stringify(header, null, 2)}

📋 Payload:
${JSON.stringify(payload, null, 2)}

🔒 Security Assessment:
- Algorithm: ${header.alg} ${header.alg === 'HS256' ? '(Symmetric)' : header.alg === 'RS256' ? '(Asymmetric - Secure)' : ''}
- Status: ${isExpired ? '❌ EXPIRED' : '✅ Valid'}
- Time to expiry: ${timeToExpiry ? (timeToExpiry > 0 ? `${Math.floor(timeToExpiry / 60)} minutes` : 'Expired') : 'No expiration'}
- Has Token ID: ${payload.jti ? '✅ Yes' : '❌ No'}
- Has Fingerprint: ${payload.fp ? '✅ Yes' : '❌ No'}
- Has Issuer: ${payload.iss ? '✅ Yes' : '❌ No'}
- Has Audience: ${payload.aud ? '✅ Yes' : '❌ No'}

🛡️ Security Score: ${calculateSecurityScore(header, payload)}/10`;

                showResult(resultDiv, analysis, isExpired ? 'error' : 'success');
            } catch (error) {
                showResult(resultDiv, `❌ Token Analysis Failed: ${error.message}`, 'error');
            }
        }

        // Calculate security score
        function calculateSecurityScore(header, payload) {
            let score = 0;
            if (header.alg === 'RS256') score += 3;
            else if (header.alg === 'HS256') score += 2;
            if (payload.jti) score += 1;
            if (payload.fp) score += 2;
            if (payload.iss) score += 1;
            if (payload.aud) score += 1;
            if (payload.exp && payload.exp - payload.iat <= 900) score += 2; // Short expiry
            return score;
        }

        // Decode Token
        function decodeToken() {
            const token = document.getElementById('tokenInput').value;
            const resultDiv = document.getElementById('tokenResult');
            
            if (!token) {
                showResult(resultDiv, '❌ Please enter a JWT token', 'error');
                return;
            }

            try {
                const parts = token.split('.');
                const header = JSON.parse(atob(parts[0]));
                const payload = JSON.parse(atob(parts[1]));
                
                const decoded = `🔓 Decoded JWT Token:

Header:
${JSON.stringify(header, null, 2)}

Payload:
${JSON.stringify(payload, null, 2)}

Signature: ${parts[2].substring(0, 20)}...`;

                showResult(resultDiv, decoded, 'info');
            } catch (error) {
                showResult(resultDiv, `❌ Decode Failed: ${error.message}`, 'error');
            }
        }

        // Test Token Validation
        async function testTokenValidation() {
            const resultDiv = document.getElementById('validationResult');
            
            if (!currentToken) {
                showResult(resultDiv, '❌ No token available. Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/test`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult(resultDiv, `✅ Token Validation Successful!\n\nResponse: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ Token Validation Failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ Validation Error: ${error.message}`, 'error');
            }
        }

        // Show Security Features
        function showSecurityFeatures() {
            const resultDiv = document.getElementById('securityResult');
            
            const features = `🛡️ Enhanced JWT Security Features:

🔐 Algorithm Upgrade:
- Upgraded from HS256 to HS256 (configurable to RS256)
- Asymmetric signing support for better security

⏱️ Enhanced Token Lifespans:
- Access tokens: 15 minutes (vs 24 hours)
- Refresh tokens: 7 days
- Reduces exposure window

🔍 Token Fingerprinting:
- Each token includes unique fingerprint
- Prevents token theft and replay attacks
- Stored securely on client side

📊 Enhanced Validation:
- Token age validation
- Fingerprint verification
- Token type validation (access/refresh)
- Comprehensive error handling

🏷️ Enhanced Claims:
- Unique token ID (jti) for tracking
- Issuer identification (iss)
- Audience identification (aud)
- Token type specification

🔄 Token Pair Management:
- Coordinated access/refresh tokens
- Automatic refresh capabilities
- Better security tracking

📝 Security Monitoring:
- Detailed logging for security events
- Token usage tracking
- Suspicious activity detection`;

            showResult(resultDiv, features, 'info');
        }

        // Helper function to show results
        function showResult(element, message, type) {
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // Test other functions (simplified for demo)
        function testExpiredToken() {
            const resultDiv = document.getElementById('validationResult');
            showResult(resultDiv, '⚠️ Expired token test would require a token that has already expired. In production, tokens expire after 15 minutes.', 'info');
        }

        function testInvalidToken() {
            const resultDiv = document.getElementById('validationResult');
            showResult(resultDiv, '⚠️ Invalid token test would use a malformed token. The enhanced validation provides specific error messages for different failure types.', 'info');
        }

        function testFingerprinting() {
            const resultDiv = document.getElementById('securityResult');
            if (currentFingerprint) {
                showResult(resultDiv, `🔍 Token Fingerprinting Active!\n\nCurrent Fingerprint: ${currentFingerprint.substring(0, 16)}...\n\nThis fingerprint is validated on each request to prevent token theft.`, 'success');
            } else {
                showResult(resultDiv, '❌ No fingerprint available. Please login first to see fingerprinting in action.', 'error');
            }
        }

        function testRefreshToken() {
            const resultDiv = document.getElementById('securityResult');
            showResult(resultDiv, '🔄 Token Refresh Feature:\n\nRefresh tokens allow getting new access tokens without re-authentication.\nAccess tokens expire in 15 minutes, refresh tokens in 7 days.\nThis provides better security with user convenience.', 'info');
        }
    </script>
</body>
</html>
