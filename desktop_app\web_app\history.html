<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT | Operation History</title>
    <link rel="stylesheet" href="text_stego.css">
    <link rel="stylesheet" href="cybersecurity_elements.css">
    <link rel="stylesheet" href="snowy_raindrops.css">
    <link rel="stylesheet" href="history.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <header>
            <div class="header-container">
                <!-- Floating snowflakes -->
                <div class="snowflake-container">
                    <!-- Single raindrops -->
                    <div class="snowflake snow-1">│</div>
                    <div class="snowflake snow-2">│</div>
                    <div class="snowflake snow-3">│</div>
                    <div class="snowflake snow-4">│</div>
                    <div class="snowflake snow-5">│</div>
                    <div class="snowflake snow-6">│</div>
                    <div class="snowflake snow-7">│</div>
                    <div class="snowflake snow-8">│</div>
                    <div class="snowflake snow-9">│</div>
                    <div class="snowflake snow-10">│</div>
                    <div class="snowflake snow-11">│</div>
                    <div class="snowflake snow-12">│</div>
                    <div class="snowflake snow-13">│</div>
                    <div class="snowflake snow-14">│</div>
                    <div class="snowflake snow-15">│</div>
                    <div class="snowflake snow-16">│</div>
                    <div class="snowflake snow-17">│</div>
                    <div class="snowflake snow-18">│</div>
                    <div class="snowflake snow-19">│</div>
                    <div class="snowflake snow-20">│</div>

                    <!-- Raindrop lines -->
                    <div class="raindrop-line rain-line-1">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-2">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-3">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-4">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-5">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-6">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-7">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-8">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-9">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-10">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-11">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-12">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-13">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-14">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-15">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-16">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-17">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-18">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-19">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-20">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-21">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-22">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-23">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-24">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-25">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-26">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-27">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-28">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-29">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-30">│<br>│<br>│</div>
                </div>

                <div class="logo">
                    <i class="fas fa-shield-alt logo-icon"></i>
                    <span class="logo-text">LESAVOT</span>
                </div>
                <div class="subtitle">THE MORE YOU LOOK, THE LESS YOU SEE</div>
            </div>
            <div class="user-info" id="userInfo">
                <span id="welcomeMessage">Welcome</span>
                <div class="user-actions">
                    <a href="profile.html" class="btn-icon" title="Profile">
                        <i class="fas fa-user"></i>
                    </a>
                    <a href="auth.html" class="btn-icon active-logout" title="Sign Out" id="signOutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </header>

        <div class="tab-navigation">
            <button type="button" class="tab-btn" onclick="window.location.href='text_stego.html'">
                <i class="fas fa-font"></i>
                <span>Text</span>
            </button>
            <button type="button" class="tab-btn" onclick="window.location.href='image_stego.html'">
                <i class="fas fa-image"></i>
                <span>Image</span>
            </button>
            <button type="button" class="tab-btn" onclick="window.location.href='audio_stego.html'">
                <i class="fas fa-volume-up"></i>
                <span>Audio</span>
            </button>
            <button type="button" class="tab-btn active">
                <i class="fas fa-history"></i>
                <span>History</span>
            </button>
        </div>

        <div id="notificationArea"></div>

        <main>
            <!-- Main Content Area -->
            <div class="content-container">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-history"></i>
                        <h2>Operation History</h2>
                    </div>

                    <div class="card-body">
                        <div class="history-filters">
                            <div class="filter-group">
                                <label for="typeFilter">Type:</label>
                                <select id="typeFilter">
                                    <option value="">All</option>
                                    <option value="text">Text</option>
                                    <option value="image">Image</option>
                                    <option value="audio">Audio</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="modeFilter">Mode:</label>
                                <select id="modeFilter">
                                    <option value="">All</option>
                                    <option value="encrypt">Encrypt</option>
                                    <option value="decrypt">Decrypt</option>
                                </select>
                            </div>
                            <button id="clearHistoryBtn" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Clear History
                            </button>
                        </div>

                        <div class="history-table-container">
                            <table class="history-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Mode</th>
                                        <th>Password</th>
                                        <th>Details</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                    <!-- History entries will be added here dynamically -->
                                    <tr class="loading-row">
                                        <td colspan="6">Loading history...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button id="prevPageBtn" class="btn btn-outline" disabled>
                                <i class="fas fa-chevron-left"></i> Previous
                            </button>
                            <span id="paginationInfo">Page 1 of 1</span>
                            <button id="nextPageBtn" class="btn btn-outline" disabled>
                                Next <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="api-client.js"></script>
    <script src="user-auth.js"></script>
    <script src="history.js"></script>
</body>
</html>
