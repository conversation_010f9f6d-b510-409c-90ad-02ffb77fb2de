<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT Authentication Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .debug-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .debug-button.success {
            background: #28a745;
        }
        .debug-button.error {
            background: #dc3545;
        }
        .result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #555;
            background: #333;
            color: #fff;
            border-radius: 4px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 LESAVOT Authentication Debug Console</h1>
    
    <div class="debug-section">
        <h2>🔍 System Status</h2>
        <button class="debug-button" onclick="checkSystemStatus()">Check System Status</button>
        <div id="systemStatus" class="result"></div>
    </div>

    <div class="debug-section">
        <h2>📝 Test Signup</h2>
        <div class="form-group">
            <label>Username:</label>
            <input type="text" id="testUsername" placeholder="Enter test username">
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="testEmail" placeholder="Enter test email">
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="testPassword" placeholder="Enter test password">
        </div>
        <button class="debug-button" onclick="testSignup()">Test Signup</button>
        <button class="debug-button" onclick="generateTestUser()">Generate Random User</button>
        <div id="signupResults" class="result"></div>
    </div>

    <div class="debug-section">
        <h2>🔐 Test Signin</h2>
        <div class="form-group">
            <label>Username:</label>
            <input type="text" id="signinUsername" placeholder="Enter username">
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="signinPassword" placeholder="Enter password">
        </div>
        <button class="debug-button" onclick="testSignin()">Test Signin</button>
        <button class="debug-button" onclick="useExistingUser()">Use Existing User</button>
        <div id="signinResults" class="result"></div>
    </div>

    <div class="debug-section">
        <h2>🌐 Network Debug</h2>
        <button class="debug-button" onclick="testNetworkConnectivity()">Test Network</button>
        <button class="debug-button" onclick="testCORSHeaders()">Test CORS</button>
        <button class="debug-button" onclick="testAllEndpoints()">Test All Endpoints</button>
        <div id="networkResults" class="result"></div>
    </div>

    <script src="config.js"></script>
    <script>
        // Configuration
        const API_BASE = 'http://localhost:3000/api/v1';
        const HEALTH_URL = 'http://localhost:3000/api/health';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            console.log(`${prefix} ${message}`);
            return `[${timestamp}] ${prefix} ${message}\n`;
        }

        function updateResults(elementId, content, append = false) {
            const element = document.getElementById(elementId);
            if (append) {
                element.innerHTML += content;
            } else {
                element.innerHTML = content;
            }
            element.scrollTop = element.scrollHeight;
        }

        async function checkSystemStatus() {
            let output = log('🔍 Checking system status...', 'info');
            updateResults('systemStatus', output);

            try {
                // Check configuration
                output += log(`CONFIG loaded: ${!!window.CONFIG}`, 'info');
                if (window.CONFIG) {
                    output += log(`Environment: ${window.CONFIG.environment}`, 'info');
                    output += log(`API Base: ${window.CONFIG.apiBaseUrl}`, 'info');
                }
                output += log(`Using API Base: ${API_BASE}`, 'info');
                updateResults('systemStatus', output);

                // Check backend health
                const healthResponse = await fetch(HEALTH_URL);
                const healthData = await healthResponse.json();
                
                if (healthResponse.ok) {
                    output += log('Backend server: ONLINE', 'success');
                    output += log(`Users: ${healthData.users}, Sessions: ${healthData.sessions}`, 'info');
                } else {
                    output += log('Backend server: ERROR', 'error');
                }
                updateResults('systemStatus', output);

                // Check frontend server
                output += log(`Frontend server: ${window.location.origin}`, 'info');
                output += log('System status check complete', 'success');
                updateResults('systemStatus', output);

            } catch (error) {
                output += log(`System check error: ${error.message}`, 'error');
                updateResults('systemStatus', output);
            }
        }

        function generateTestUser() {
            const timestamp = Date.now();
            document.getElementById('testUsername').value = `testuser_${timestamp}`;
            document.getElementById('testEmail').value = `test_${timestamp}@example.com`;
            document.getElementById('testPassword').value = 'TestPassword123!';
            log('Generated random test user', 'info');
        }

        function useExistingUser() {
            // Use a user from the backend logs
            document.getElementById('signinUsername').value = 'TCHANGA BECHI Jacques';
            document.getElementById('signinPassword').value = 'TestPassword123!';
            log('Using existing user from backend', 'info');
        }

        async function testSignup() {
            const username = document.getElementById('testUsername').value;
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;

            if (!username || !email || !password) {
                updateResults('signupResults', log('Please fill in all fields', 'error'));
                return;
            }

            let output = log('📝 Testing signup...', 'info');
            updateResults('signupResults', output);

            try {
                const signupData = {
                    username,
                    email,
                    password,
                    confirmPassword: password
                };

                output += log(`Signup URL: ${API_BASE}/auth/signup`, 'info');
                output += log(`Data: ${JSON.stringify({...signupData, password: '***', confirmPassword: '***'})}`, 'info');
                updateResults('signupResults', output);

                const response = await fetch(`${API_BASE}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(signupData)
                });

                output += log(`Response status: ${response.status}`, 'info');
                const data = await response.json();
                output += log(`Response: ${JSON.stringify(data, null, 2)}`, 'info');

                if (response.ok && data.success) {
                    output += log('Signup successful!', 'success');
                    output += log(`User ID: ${data.user.id}`, 'success');
                    // Auto-fill signin form
                    document.getElementById('signinUsername').value = username;
                    document.getElementById('signinPassword').value = password;
                } else {
                    output += log(`Signup failed: ${data.message}`, 'error');
                }

                updateResults('signupResults', output);

            } catch (error) {
                output += log(`Signup error: ${error.message}`, 'error');
                updateResults('signupResults', output);
            }
        }

        async function testSignin() {
            const username = document.getElementById('signinUsername').value;
            const password = document.getElementById('signinPassword').value;

            if (!username || !password) {
                updateResults('signinResults', log('Please fill in username and password', 'error'));
                return;
            }

            let output = log('🔐 Testing signin...', 'info');
            updateResults('signinResults', output);

            try {
                const signinData = { username, password };

                output += log(`Signin URL: ${API_BASE}/auth/signin`, 'info');
                output += log(`Data: ${JSON.stringify({...signinData, password: '***'})}`, 'info');
                updateResults('signinResults', output);

                const response = await fetch(`${API_BASE}/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(signinData)
                });

                output += log(`Response status: ${response.status}`, 'info');
                const data = await response.json();
                output += log(`Response: ${JSON.stringify(data, null, 2)}`, 'info');

                if (response.ok && data.success) {
                    output += log('Signin successful!', 'success');
                    output += log(`Session token: ${data.sessionToken.substring(0, 20)}...`, 'success');
                    output += log(`User: ${data.user.username}`, 'success');
                } else {
                    output += log(`Signin failed: ${data.message}`, 'error');
                }

                updateResults('signinResults', output);

            } catch (error) {
                output += log(`Signin error: ${error.message}`, 'error');
                updateResults('signinResults', output);
            }
        }

        async function testNetworkConnectivity() {
            let output = log('🌐 Testing network connectivity...', 'info');
            updateResults('networkResults', output);

            try {
                // Test health endpoint
                const healthStart = Date.now();
                const healthResponse = await fetch(HEALTH_URL);
                const healthTime = Date.now() - healthStart;
                
                output += log(`Health check: ${healthResponse.status} (${healthTime}ms)`, 
                    healthResponse.ok ? 'success' : 'error');

                // Test CORS preflight
                const corsResponse = await fetch(`${API_BASE}/auth/signup`, {
                    method: 'OPTIONS'
                });
                output += log(`CORS preflight: ${corsResponse.status}`, 
                    corsResponse.ok ? 'success' : 'warning');

                updateResults('networkResults', output);

            } catch (error) {
                output += log(`Network error: ${error.message}`, 'error');
                updateResults('networkResults', output);
            }
        }

        async function testCORSHeaders() {
            let output = log('🔍 Testing CORS headers...', 'info');
            updateResults('networkResults', output);

            try {
                const response = await fetch(HEALTH_URL);
                
                const corsHeaders = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods',
                    'Access-Control-Allow-Headers',
                    'Access-Control-Allow-Credentials'
                ];

                corsHeaders.forEach(header => {
                    const value = response.headers.get(header);
                    output += log(`${header}: ${value || 'Not set'}`, value ? 'info' : 'warning');
                });

                updateResults('networkResults', output);

            } catch (error) {
                output += log(`CORS test error: ${error.message}`, 'error');
                updateResults('networkResults', output);
            }
        }

        async function testAllEndpoints() {
            let output = log('🧪 Testing all endpoints...', 'info');
            updateResults('networkResults', output);

            const endpoints = [
                { url: HEALTH_URL, method: 'GET', name: 'Health Check' },
                { url: `${API_BASE}/auth/signup`, method: 'POST', name: 'Signup Endpoint' },
                { url: `${API_BASE}/auth/signin`, method: 'POST', name: 'Signin Endpoint' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, {
                        method: endpoint.method === 'GET' ? 'GET' : 'OPTIONS'
                    });
                    
                    output += log(`${endpoint.name}: ${response.status}`, 
                        response.ok ? 'success' : 'error');
                    
                } catch (error) {
                    output += log(`${endpoint.name}: ERROR - ${error.message}`, 'error');
                }
            }

            updateResults('networkResults', output);
        }

        // Auto-run system check on load
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 500);
        });
    </script>
</body>
</html>
