const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

// Simple user storage (in-memory for testing)
const userStorage = new Map();

// Password hashing (simple for demo - use bcrypt in production)
const crypto = require('crypto');

function hashPassword(password) {
    return crypto.createHash('sha256').update(password).digest('hex');
}

function verifyPassword(password, hashedPassword) {
    return hashPassword(password) === hashedPassword;
}

// Generate session token (simple for testing)
function generateSessionToken(username) {
    return Buffer.from(`${username}:${Date.now()}`).toString('base64');
}

// Authentication Routes
app.post('/api/auth/signup', (req, res) => {
    try {
        const { username, email, password, confirmPassword } = req.body;

        // Validation
        if (!username || username.length < 3) {
            return res.status(400).json({
                status: 'error',
                message: 'Username must be at least 3 characters long'
            });
        }

        if (!email || !email.includes('@')) {
            return res.status(400).json({
                status: 'error',
                message: 'Valid email is required'
            });
        }

        if (!password || password.length < 6) {
            return res.status(400).json({
                status: 'error',
                message: 'Password must be at least 6 characters long'
            });
        }

        if (password !== confirmPassword) {
            return res.status(400).json({
                status: 'error',
                message: 'Passwords do not match'
            });
        }

        // Check if username already exists
        const existingUser = Array.from(userStorage.values()).find(user => user.username === username);
        if (existingUser) {
            return res.status(400).json({
                status: 'error',
                message: 'Username already exists. Please choose a different username.'
            });
        }

        // Check if email already exists
        if (userStorage.has(email)) {
            return res.status(400).json({
                status: 'error',
                message: 'Email already exists. Please sign in instead.'
            });
        }

        // Create new user
        const user = {
            id: Date.now(),
            username,
            email,
            password: hashPassword(password),
            displayName: username,
            isVerified: true,
            createdAt: new Date().toISOString(),
            lastLogin: new Date().toISOString()
        };

        userStorage.set(email, user);

        // Generate session token
        const sessionToken = generateSessionToken(username);

        console.log(`✅ New user created: ${username} (${email})`);

        res.json({
            status: 'success',
            message: 'Account created successfully! Welcome to LESAVOT!',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                displayName: user.displayName,
                createdAt: user.createdAt
            },
            sessionToken
        });
    } catch (error) {
        console.error('Signup error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to create account'
        });
    }
});

app.post('/api/auth/signin', (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username) {
            return res.status(400).json({
                status: 'error',
                message: 'Username is required'
            });
        }

        if (!password) {
            return res.status(400).json({
                status: 'error',
                message: 'Password is required'
            });
        }

        // Find user by username
        const user = Array.from(userStorage.values()).find(user => user.username === username);
        if (!user) {
            return res.status(401).json({
                status: 'error',
                message: 'Invalid username or password'
            });
        }

        // Verify password
        if (!verifyPassword(password, user.password)) {
            return res.status(401).json({
                status: 'error',
                message: 'Invalid username or password'
            });
        }

        // Update last login
        user.lastLogin = new Date().toISOString();
        userStorage.set(user.email, user);

        // Generate session token
        const sessionToken = generateSessionToken(username);

        console.log(`✅ User signed in: ${username} (${user.email})`);

        res.json({
            status: 'success',
            message: 'Sign in successful! Welcome back!',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                displayName: user.displayName,
                lastLogin: user.lastLogin
            },
            sessionToken
        });
    } catch (error) {
        console.error('Signin error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to sign in'
        });
    }
});



// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'success',
        message: 'LESAVOT Authentication Server is running',
        timestamp: new Date().toISOString(),
        users: userStorage.size
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`✅ LESAVOT Authentication Server running on port ${PORT}`);
    console.log(`✅ Frontend: http://localhost:${PORT}`);
    console.log(`✅ API: http://localhost:${PORT}/api`);
    console.log(`✅ Health: http://localhost:${PORT}/api/health`);
    console.log('🔐 Simple Username/Password Authentication Active');
});

module.exports = app;
