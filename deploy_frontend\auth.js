// LESAVOT - Authentication Page JavaScript (with secure password hashing and advanced toggle)

// SHA-256 hashing utility
async function hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
    return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
}

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const signInForm = document.getElementById('signInForm');
    const signUpForm = document.getElementById('signUpForm');
    const showSignUpBtn = document.getElementById('showSignUpBtn');
    const showSignInBtn = document.getElementById('showSignInBtn');
    const signInBtn = document.getElementById('signInBtn');
    const signUpBtn = document.getElementById('signUpBtn');
    const signUpPassword = document.getElementById('signUpPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const strengthSegments = document.querySelectorAll('.strength-segment');
    const strengthText = document.querySelector('.strength-text');
    const signInPassword = document.getElementById('signInPassword');

    // OTP Modal Elements (dynamically created)
    let otpModal = null;
    let otpInput = null;
    let otpSubmitBtn = null;
    let otpUsername = '';

    // Password visibility toggle state for each field
    const passwordToggleStates = {
        signInPassword: 0,
        signUpPassword: 0,
        confirmPassword: 0
    };

    // Secure, accessible password toggle logic
    function togglePasswordVisibility(input, toggleBtn, stateKey) {
        if (passwordToggleStates[stateKey] === 0) {
            input.type = 'text';
            toggleBtn.classList.remove('fa-eye');
            toggleBtn.classList.add('fa-eye-slash');
            toggleBtn.setAttribute('aria-label', 'Hide password');
            passwordToggleStates[stateKey] = 1;
        } else {
            input.type = 'password';
            toggleBtn.classList.remove('fa-eye-slash');
            toggleBtn.classList.add('fa-eye');
            toggleBtn.setAttribute('aria-label', 'Show password');
            passwordToggleStates[stateKey] = 0;
        }
    }

    // Password suggestion logic
    function generateStrongPassword() {
        // 20+ chars, mix of upper, lower, digits, symbols
        const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lower = 'abcdefghijklmnopqrstuvwxyz';
        const digits = '0123456789';
        const symbols = '!@#$%^&*()-_=+[]{}|;:,.<>?';
        const all = upper + lower + digits + symbols;
        let password = '';
        // Ensure at least one of each type
        password += upper[Math.floor(Math.random() * upper.length)];
        password += lower[Math.floor(Math.random() * lower.length)];
        password += digits[Math.floor(Math.random() * digits.length)];
        password += symbols[Math.floor(Math.random() * symbols.length)];
        for (let i = 4; i < 24; i++) {
            password += all[Math.floor(Math.random() * all.length)];
        }
        // Shuffle password
        password = password.split('').sort(() => 0.5 - Math.random()).join('');
        return password;
    }

    // Add suggest password button to signup form
    const suggestBtn = document.createElement('button');
    suggestBtn.type = 'button';
    suggestBtn.className = 'btn btn-outline password-suggest-btn';
    suggestBtn.textContent = 'Suggest Strong Password';
    const signUpPasswordGroup = signUpPassword.closest('.form-group');
    if (signUpPasswordGroup) {
        signUpPasswordGroup.appendChild(suggestBtn);
    }
    suggestBtn.addEventListener('click', function() {
        const strongPassword = generateStrongPassword();
        signUpPassword.value = strongPassword;
        confirmPassword.value = strongPassword;
        signUpPassword.dispatchEvent(new Event('input'));
        showNotification('A strong password has been suggested. You can use or edit it.', 'info');
    });

    // Password toggle event listeners (consistent, accessible, secure)
    const signInPasswordToggle = document.getElementById('signInPasswordToggle');
    if (signInPasswordToggle) {
        signInPasswordToggle.setAttribute('tabindex', '0');
        signInPasswordToggle.setAttribute('role', 'button');
        signInPasswordToggle.setAttribute('aria-label', 'Show password');
        signInPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(signInPassword, this, 'signInPassword');
        });
        signInPasswordToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility(signInPassword, this, 'signInPassword');
            }
        });
    }
    const signUpPasswordToggle = document.getElementById('signUpPasswordToggle');
    if (signUpPasswordToggle) {
        signUpPasswordToggle.setAttribute('tabindex', '0');
        signUpPasswordToggle.setAttribute('role', 'button');
        signUpPasswordToggle.setAttribute('aria-label', 'Show password');
        signUpPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(signUpPassword, this, 'signUpPassword');
        });
        signUpPasswordToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility(signUpPassword, this, 'signUpPassword');
            }
        });
    }
    const confirmPasswordToggle = document.getElementById('confirmPasswordToggle');
    if (confirmPasswordToggle) {
        confirmPasswordToggle.setAttribute('tabindex', '0');
        confirmPasswordToggle.setAttribute('role', 'button');
        confirmPasswordToggle.setAttribute('aria-label', 'Show password');
        confirmPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(confirmPassword, this, 'confirmPassword');
        });
        confirmPasswordToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility(confirmPassword, this, 'confirmPassword');
            }
        });
    }

    // Toggle between sign in and sign up forms
    showSignUpBtn.addEventListener('click', function(e) {
        e.preventDefault();
        signInForm.classList.remove('active');
        signUpForm.classList.add('active');
        // Clear signup fields
        document.getElementById('signUpFullName').value = '';
        document.getElementById('signUpUsername').value = '';
        document.getElementById('signUpEmail').value = '';
        document.getElementById('signUpPassword').value = '';
        document.getElementById('confirmPassword').value = '';
    });

    // On page load, ensure signup fields are empty
    document.getElementById('signUpFullName').value = '';
    document.getElementById('signUpUsername').value = '';
    document.getElementById('signUpEmail').value = '';
    document.getElementById('signUpPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    showSignInBtn.addEventListener('click', function(e) {
        e.preventDefault();
        signUpForm.classList.remove('active');
        signInForm.classList.add('active');
        // Clear sign in fields
        document.getElementById('signInUsername').value = '';
        document.getElementById('signInPassword').value = '';
    });

    // On page load, ensure sign in fields are empty
    document.getElementById('signInUsername').value = '';
    document.getElementById('signInPassword').value = '';

    // Autofill + fingerprint for password field
    const signInUsername = document.getElementById('signInUsername');
    const signInPasswordInput = document.getElementById('signInPassword');
    let autofillFingerprintRequested = false;
    signInPasswordInput.addEventListener('input', async function(e) {
        // Detect autofill: if both username and password are filled quickly, likely autofill
        if (
            signInUsername.value &&
            signInPasswordInput.value &&
            !autofillFingerprintRequested &&
            (document.activeElement !== signInPasswordInput)
        ) {
            autofillFingerprintRequested = true;
            // Prompt for fingerprint authentication (WebAuthn)
            if (window.PublicKeyCredential) {
                try {
                    await navigator.credentials.get({ publicKey: { challenge: new Uint8Array(32), timeout: 60000, userVerification: 'required' } });
                    showNotification('Fingerprint verified. You may now sign in.', 'success');
                } catch (e) {
                    showNotification('Fingerprint authentication failed or was cancelled.', 'error');
                }
            }
        }
    });
    signInPasswordInput.addEventListener('keydown', function() {
        autofillFingerprintRequested = false;
    });

    // Password strength meter
    signUpPassword.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updateStrengthMeter(strength);
    });

    // Password visibility toggles
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const passwordInput = this.parentElement.querySelector('input[type="password"], input[type="text"]');
            const isPassword = passwordInput.type === 'password';

            passwordInput.type = isPassword ? 'text' : 'password';
            this.classList.toggle('fa-eye');
            this.classList.toggle('fa-eye-slash');
        });
    });

    // Calculate password strength (0-4)
    function calculatePasswordStrength(password) {
        if (!password) return 0;
        let strength = 0;
        if (password.length >= 8) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        return Math.min(strength, 4);
    }
    function updateStrengthMeter(strength) {
        strengthSegments.forEach(segment => { segment.className = 'strength-segment'; });
        for (let i = 0; i < strength; i++) {
            if (i < strengthSegments.length) {
                if (strength === 1) strengthSegments[i].classList.add('weak');
                else if (strength === 2) strengthSegments[i].classList.add('medium');
                else if (strength === 3) strengthSegments[i].classList.add('good');
                else if (strength === 4) strengthSegments[i].classList.add('strong');
            }
        }
        if (strength === 0) strengthText.textContent = 'Password strength';
        else if (strength === 1) strengthText.textContent = 'Weak password';
        else if (strength === 2) strengthText.textContent = 'Medium password';
        else if (strength === 3) strengthText.textContent = 'Good password';
        else strengthText.textContent = 'Strong password';
    }

    // Sign Up button click handler
    signUpBtn.addEventListener('click', async function() {
        const fullName = document.getElementById('signUpFullName').value.trim();
        const username = document.getElementById('signUpUsername').value.trim();
        const email = document.getElementById('signUpEmail').value.trim();
        const password = document.getElementById('signUpPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const agreeTerms = document.getElementById('agreeTerms').checked;

        // Validation
        if (!fullName) {
            showNotification('Please enter your full name', 'error');
            return;
        }

        if (!username) {
            showNotification('Please enter a username', 'error');
            return;
        }

        if (!email) {
            showNotification('Please enter your email address', 'error');
            return;
        }

        if (!isValidEmail(email)) {
            showNotification('Please enter a valid email address', 'error');
            return;
        }

        if (!password) {
            showNotification('Please enter a password', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showNotification('Passwords do not match', 'error');
            return;
        }

        if (!agreeTerms) {
            showNotification('Please agree to the Terms of Service and Privacy Policy', 'error');
            return;
        }

        showNotification('Creating your account...', 'info');
        try {
            const hashedPassword = await hashPassword(password);

            const response = await fetch('/api/auth/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fullName,
                    username,
                    email,
                    password: hashedPassword
                })
            });

            const data = await response.json();

            if (response.ok) {
                showNotification('Account created successfully! Please sign in.', 'success');
                // Switch to sign in form
                signUpForm.classList.remove('active');
                signInForm.classList.add('active');
                // Pre-fill username
                document.getElementById('signInUsername').value = username;
            } else {
                showNotification(data.message || 'Failed to create account', 'error');
            }
        } catch (error) {
            console.error('Signup error:', error);
            showNotification('An error occurred during signup. Please try again.', 'error');
        }
    });

    // Sign In button click handler
    signInBtn.addEventListener('click', async function() {
        const username = document.getElementById('signInUsername').value.trim();
        const password = document.getElementById('signInPassword').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!username) {
            showNotification('Please enter your username', 'error');
            return;
        }

        if (!password) {
            showNotification('Please enter your password', 'error');
            return;
        }

        showNotification('Verifying credentials...', 'info');
        try {
            const hashedPassword = await hashPassword(password);

            const response = await fetch('/api/auth/signin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    password: hashedPassword
                })
            });

            const data = await response.json();

            if (response.ok && data.requiresOtp) {
                // Store username for OTP verification
                otpUsername = username;

                // Remember user if checkbox is checked
                if (rememberMe) {
                    localStorage.setItem('rememberUser', 'true');
                    localStorage.setItem('username', username);
                } else {
                    localStorage.removeItem('rememberUser');
                    localStorage.removeItem('username');
                }

                showNotification('Authentication successful! JWT OTP has been sent to your email. Please click the link in your email to return here and complete verification.', 'success');

                // Show OTP modal for code entry after email verification
                setTimeout(() => {
                    showOtpModal();
                }, 2000);

            } else if (response.ok) {
                // Direct login without OTP
                showNotification('Sign in successful!', 'success');
                if (data.token) {
                    localStorage.setItem('jwt_token', data.token);
                    if (data.refreshToken) {
                        localStorage.setItem('refresh_token', data.refreshToken);
                    }
                }
                localStorage.setItem('username', username);
                setTimeout(() => { window.location.href = 'text_stego.html'; }, 1000);
            } else {
                showNotification(data.message || 'Invalid credentials', 'error');
            }
        } catch (error) {
            console.error('Signin error:', error);
            showNotification('An error occurred during sign in. Please try again.', 'error');
        }
    });



    // Show OTP modal/input with Resend OTP feature
    function showOtpModal() {
        if (otpModal) otpModal.remove();
        otpModal = document.createElement('div');
        otpModal.className = 'otp-modal';
        otpModal.innerHTML = `
            <div class="otp-modal-content">
                <h3>Enter OTP</h3>
                <input type="text" id="otpInput" maxlength="6" placeholder="Enter 6-digit OTP" autocomplete="one-time-code" />
                <button id="otpSubmitBtn" class="btn btn-primary">Verify OTP</button>
                <button id="otpResendBtn" class="btn btn-link">Resend OTP</button>
                <span id="otpResendCooldown" style="margin-left:10px;color:#888;"></span>
                <button id="otpCancelBtn" class="btn btn-secondary">Cancel</button>
            </div>
        `;
        document.body.appendChild(otpModal);
        otpInput = document.getElementById('otpInput');
        otpSubmitBtn = document.getElementById('otpSubmitBtn');
        const otpResendBtn = document.getElementById('otpResendBtn');
        const otpResendCooldown = document.getElementById('otpResendCooldown');
        const otpCancelBtn = document.getElementById('otpCancelBtn');
        otpInput.focus();
        otpSubmitBtn.addEventListener('click', submitOtp);
        otpCancelBtn.addEventListener('click', function() {
            otpModal.remove();
        });
        otpInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') submitOtp();
        });
        // Resend OTP logic with cooldown
        let resendCooldown = 0;
        let resendInterval = null;
        function startResendCooldown() {
            resendCooldown = 10;
            otpResendBtn.disabled = true;
            otpResendBtn.style.pointerEvents = 'none';
            otpResendCooldown.textContent = `Resend available in ${resendCooldown}s`;
            resendInterval = setInterval(() => {
                resendCooldown--;
                if (resendCooldown > 0) {
                    otpResendCooldown.textContent = `Resend available in ${resendCooldown}s`;
                } else {
                    clearInterval(resendInterval);
                    otpResendBtn.disabled = false;
                    otpResendBtn.style.pointerEvents = 'auto';
                    otpResendCooldown.textContent = '';
                }
            }, 1000);
        }
        otpResendBtn.addEventListener('click', async function() {
            otpResendBtn.disabled = true;
            otpResendBtn.style.pointerEvents = 'none';
            showNotification('Resending OTP...', 'info');
            try {
                // Use proper API endpoint for OTP resend (same as login)
                const apiUrl = window.CONFIG ? window.CONFIG.getApiUrl('auth/login') : '/api/v1/auth/login';
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: otpUsername, resendOtp: true })
                });
                const data = await response.json();
                if (response.ok) {
                    showNotification('A new OTP has been sent to your email.', 'success');
                    startResendCooldown();
                } else {
                    showNotification(data.message || 'Failed to resend OTP', 'error');
                    otpResendBtn.disabled = false;
                    otpResendBtn.style.pointerEvents = 'auto';
                }
            } catch (error) {
                showNotification('Error resending OTP. Please try again.', 'error');
                otpResendBtn.disabled = false;
                otpResendBtn.style.pointerEvents = 'auto';
            }
        });
        startResendCooldown(); // Start cooldown on modal open
    }

    // Submit OTP to backend
    async function submitOtp() {
        const otp = otpInput.value.trim();
        if (!otp || otp.length !== 6) {
            showNotification('Please enter the 6-digit OTP sent to your email.', 'error');
            return;
        }
        try {
            // Use proper API endpoint for OTP verification
            const apiUrl = window.CONFIG ? window.CONFIG.getApiUrl('auth/verify-otp') : '/api/v1/auth/verify-otp';
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username: otpUsername, otp })
            });
            const data = await response.json();
            if (response.ok) {
                showNotification('OTP verified. Access granted.', 'success');
                otpModal.remove();
                // Store authentication token and user info
                if (data.token) {
                    localStorage.setItem('jwt_token', data.token);
                    if (data.refreshToken) {
                        localStorage.setItem('refresh_token', data.refreshToken);
                    }
                }
                localStorage.setItem('username', otpUsername);
                setTimeout(() => { window.location.href = 'text_stego.html'; }, 1000);
            } else {
                showNotification(data.message || 'Invalid OTP', 'error');
            }
        } catch (error) {
            console.error('OTP verification error:', error);
            showNotification('An error occurred during OTP verification. Please try again.', 'error');
        }
    }

    // Helper functions (showNotification, isValidEmail, handleSuccessfulLogin, etc.)
    function showNotification(message, type = 'info') {
        const notificationArea = document.getElementById('notificationArea');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        const notificationContent = document.createElement('div');
        notificationContent.className = 'notification-content';
        const header = document.createElement('div');
        header.className = 'notification-header';
        header.innerHTML = `<i class="fas fa-${icon}"></i><span class="notification-title">${type.charAt(0).toUpperCase() + type.slice(1)}</span>`;
        const messageElement = document.createElement('p');
        messageElement.className = 'notification-message';
        messageElement.textContent = message;
        notificationContent.appendChild(header);
        notificationContent.appendChild(messageElement);
        notification.appendChild(notificationContent);
        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        notification.appendChild(closeBtn);
        notificationArea.appendChild(notification);
        closeBtn.addEventListener('click', function() {
            notification.style.opacity = '0';
            setTimeout(() => { notification.remove(); }, 300);
        });
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                setTimeout(() => { if (notification.parentNode) notification.remove(); }, 300);
            }
        }, 5000);
    }
    function isValidEmail(email) {
        const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return re.test(email);
    }
    function handleSuccessfulLogin(user, rememberMe) {
        const displayName = user.fullName || user.username;
        showNotification(`Welcome back, ${displayName}!`, 'success');
        if (rememberMe) showNotification('Login credentials will be remembered', 'info');
        localStorage.setItem('username', user.username);
        if (rememberMe) localStorage.setItem('rememberUser', 'true');
        setTimeout(() => { window.location.href = 'text_stego.html'; }, 1000);
    }

    // Forgot Password functionality
    const forgotPasswordLink = document.querySelector('.forgot-password');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            showForgotPasswordModal();
        });
    }

    // Show forgot password modal
    function showForgotPasswordModal() {
        // Create modal HTML
        const modalHTML = `
            <div class="modal-overlay" id="forgotPasswordModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-key"></i> Reset Password</h3>
                        <button class="modal-close" id="closeForgotPasswordModal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="modal-description">
                            Enter your email address and we'll send you a secure link to reset your password.
                        </p>
                        <div class="form-group">
                            <label for="resetEmail">Email Address</label>
                            <div class="input-group">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" id="resetEmail" name="resetEmail"
                                       autocomplete="email" required>
                            </div>
                        </div>
                        <button type="button" class="auth-btn" id="sendResetBtn">
                            <i class="fas fa-paper-plane"></i>
                            Send Reset Link
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = document.getElementById('forgotPasswordModal');
        const closeBtn = document.getElementById('closeForgotPasswordModal');
        const sendBtn = document.getElementById('sendResetBtn');
        const emailInput = document.getElementById('resetEmail');

        // Close modal handlers
        closeBtn.addEventListener('click', () => modal.remove());
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });

        // Send reset email handler
        sendBtn.addEventListener('click', async function() {
            const email = emailInput.value.trim();

            if (!email) {
                showNotification('Please enter your email address', 'error');
                return;
            }

            if (!isValidEmail(email)) {
                showNotification('Please enter a valid email address', 'error');
                return;
            }

            sendBtn.disabled = true;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';

            try {
                const apiUrl = window.CONFIG ? window.CONFIG.getApiUrl('auth/forgot-password') : '/api/auth/forgot-password';
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email })
                });

                const data = await response.json();

                if (response.ok) {
                    showNotification('If an account with that email exists, a password reset link has been sent.', 'success');
                    modal.remove();
                } else {
                    showNotification(data.message || 'Failed to send reset email', 'error');
                }
            } catch (error) {
                console.error('Forgot password error:', error);
                showNotification('An error occurred. Please try again.', 'error');
            } finally {
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Reset Link';
            }
        });

        // Focus email input
        setTimeout(() => emailInput.focus(), 100);
    }
});
