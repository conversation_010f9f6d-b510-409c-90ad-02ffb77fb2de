<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT - Offline</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="favicon.svg" type="image/svg+xml">
    <style>
        .offline-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 2rem;
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .offline-icon {
            font-size: 5rem;
            margin-bottom: 2rem;
            color: var(--accent-color);
        }

        .offline-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .offline-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            max-width: 600px;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .offline-button {
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .offline-button:hover {
            background-color: var(--primary-dark);
        }

        .offline-features {
            margin-top: 3rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 900px;
        }

        .offline-feature {
            background-color: var(--card-bg);
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .offline-feature h3 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .offline-title {
                font-size: 2rem;
            }

            .offline-message {
                font-size: 1rem;
            }

            .offline-features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="1" y1="1" x2="23" y2="23"></line>
                <path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path>
                <path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path>
                <path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path>
                <path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path>
                <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                <line x1="12" y1="20" x2="12.01" y2="20"></line>
            </svg>
        </div>

        <h1 class="offline-title">You're Offline</h1>

        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - LESAVOT has offline capabilities, and some features will still work.
        </p>

        <div class="offline-actions">
            <button class="offline-button" id="retry-button">Retry Connection</button>
            <button class="offline-button" id="home-button">Go to Home</button>
        </div>

        <div class="offline-features">
            <div class="offline-feature">
                <h3>Available Offline</h3>
                <ul>
                    <li>Text steganography</li>
                    <li>Previously loaded images</li>
                    <li>Local history</li>
                </ul>
            </div>

            <div class="offline-feature">
                <h3>Requires Connection</h3>
                <ul>
                    <li>User authentication</li>
                    <li>Cloud history sync</li>
                    <li>New image/audio uploads</li>
                </ul>
            </div>

            <div class="offline-feature">
                <h3>Offline Mode</h3>
                <p>Your operations will be saved locally and synced when you're back online.</p>
            </div>
        </div>
    </div>

    <script>
        // Check if we're online and redirect to home if we are
        if (navigator.onLine) {
            window.location.href = '/';
        }

        // Add event listeners
        document.getElementById('retry-button').addEventListener('click', () => {
            window.location.reload();
        });

        document.getElementById('home-button').addEventListener('click', () => {
            window.location.href = '/';
        });

        // Listen for online event
        window.addEventListener('online', () => {
            window.location.reload();
        });
    </script>
</body>
</html>
