<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LESAVOT - Secure Steganography{% endblock %}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/cyber-theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/cyber-components.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header class="cyber-header">
        <div class="header-container">
            <a href="{{ url_for('index') }}" class="cyber-logo">
                <span class="logo-icon">🔐</span>LESAVOT
            </a>
            <nav class="cyber-nav">
                {% if session.username %}
                    <a href="{{ url_for('dashboard') }}" class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" data-nav>Dashboard</a>
                    <a href="{{ url_for('image') }}" class="nav-link {% if request.endpoint == 'image' %}active{% endif %}" data-nav>Image</a>
                    <a href="{{ url_for('text') }}" class="nav-link {% if request.endpoint == 'text' %}active{% endif %}" data-nav>Text</a>
                    <a href="{{ url_for('audio') }}" class="nav-link {% if request.endpoint == 'audio' %}active{% endif %}" data-nav>Audio</a>
                    <a href="{{ url_for('files') }}" class="nav-link {% if request.endpoint == 'files' %}active{% endif %}" data-nav>Files</a>
                    <a href="{{ url_for('logout') }}" class="auth-btn">Logout</a>
                {% else %}
                    <a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" data-nav>Home</a>
                    <a href="{{ url_for('login') }}" class="nav-link {% if request.endpoint == 'login' %}active{% endif %}" data-nav>Login</a>
                    <a href="{{ url_for('signup') }}" class="auth-btn">Sign Up</a>
                {% endif %}
            </nav>
        </div>
    </header>

    <main class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>
    
    <footer class="cyber-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <div class="footer-logo">LESAVOT</div>
                    <div class="footer-tagline">Secure Steganography Platform</div>
                </div>
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Contact</a>
                </div>
            </div>
            <div class="footer-copyright">
                © 2025 LESAVOT - All rights reserved
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/cyber-navigation.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
