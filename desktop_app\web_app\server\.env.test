# Test Environment Configuration

# Node Environment
NODE_ENV=test

# Server Configuration
PORT=3001
API_VERSION=v1
LOG_LEVEL=error
SHOW_STACK_TRACES=true
LOG_ERRORS=true

# Database Configuration
SUPABASE_URL=https://ighzbjltykorktwemkli.supabase.co
SUPABASE_SERVICE_KEY=your-service-key-here
SUPABASE_JWT_SECRET=your-jwt-secret-here

# Authentication Configuration
JWT_SECRET=test-jwt-secret-for-testing-only
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=test-refresh-secret-for-testing-only
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
ENABLE_HELMET=true
ENABLE_CONTENT_SECURITY_POLICY=false
ENABLE_XSS_PROTECTION=true
ENABLE_CSRF_PROTECTION=false
ENABLE_RATE_LIMITING=false
ENABLE_REQUEST_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=false

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:5000
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=Content-Type,Authorization

# Monitoring Configuration
PERFORMANCE_MONITORING_INTERVAL_MS=60000
