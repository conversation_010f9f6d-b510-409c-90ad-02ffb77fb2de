<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .form-container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #555;
            background: #333;
            color: #fff;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug {
            background: #333;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            flex: 1;
            padding: 10px;
            background: #333;
            border: none;
            color: #fff;
            cursor: pointer;
        }
        .tab.active {
            background: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>🔐 Complete Authentication Test</h1>

    <div class="tabs">
        <button class="tab active" onclick="showTab('signup')">Sign Up</button>
        <button class="tab" onclick="showTab('signin')">Sign In</button>
        <button class="tab" onclick="showTab('debug')">Debug</button>
    </div>

    <!-- Sign Up Tab -->
    <div id="signup" class="tab-content active">
        <div class="form-container">
            <h2>Create Account</h2>
            <form id="signupForm">
                <div class="form-group">
                    <label for="signupUsername">Username:</label>
                    <input type="text" id="signupUsername" required>
                </div>
                <div class="form-group">
                    <label for="signupEmail">Email:</label>
                    <input type="email" id="signupEmail" required>
                </div>
                <div class="form-group">
                    <label for="signupPassword">Password:</label>
                    <input type="password" id="signupPassword" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password:</label>
                    <input type="password" id="confirmPassword" required>
                </div>
                <button type="submit">Create Account</button>
            </form>
            <div id="signupMessage"></div>
        </div>
    </div>

    <!-- Sign In Tab -->
    <div id="signin" class="tab-content">
        <div class="form-container">
            <h2>Sign In</h2>
            <form id="signinForm">
                <div class="form-group">
                    <label for="signinUsername">Username:</label>
                    <input type="text" id="signinUsername" required>
                </div>
                <div class="form-group">
                    <label for="signinPassword">Password:</label>
                    <input type="password" id="signinPassword" required>
                </div>
                <button type="submit">Sign In</button>
            </form>
            <div id="signinMessage"></div>
        </div>
    </div>

    <!-- Debug Tab -->
    <div id="debug" class="tab-content">
        <div class="form-container">
            <h2>Debug Information</h2>
            <button onclick="runDiagnostics()">Run Diagnostics</button>
            <button onclick="clearDebug()">Clear Debug</button>
            <div id="debugOutput" class="debug"></div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3000/api/v1';
        
        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Debug logging
        function debugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            
            const debugOutput = document.getElementById('debugOutput');
            debugOutput.textContent += logMessage;
            debugOutput.scrollTop = debugOutput.scrollHeight;
            
            console.log(logMessage);
        }

        // Message display
        function showMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="message ${type}">${message}</div>`;
            
            // Auto-clear after 5 seconds
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }

        // Sign Up Handler
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('signupUsername').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            debugLog(`Starting signup for user: ${username}`);

            // Validation
            if (password !== confirmPassword) {
                showMessage('signupMessage', 'Passwords do not match', 'error');
                debugLog('Signup failed: Passwords do not match', 'error');
                return;
            }

            showMessage('signupMessage', 'Creating account...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        confirmPassword
                    })
                });

                debugLog(`Signup response status: ${response.status}`);
                const data = await response.json();
                debugLog(`Signup response: ${JSON.stringify(data, null, 2)}`);

                if (response.ok && data.success) {
                    showMessage('signupMessage', 'Account created successfully!', 'success');
                    debugLog('Signup successful', 'success');
                    
                    // Auto-fill signin form
                    document.getElementById('signinUsername').value = username;
                    document.getElementById('signinPassword').value = password;
                    
                    // Switch to signin tab
                    setTimeout(() => {
                        showTab('signin');
                        document.querySelector('.tab:nth-child(2)').click();
                    }, 2000);
                    
                } else {
                    showMessage('signupMessage', data.message || 'Failed to create account', 'error');
                    debugLog(`Signup failed: ${data.message}`, 'error');
                }

            } catch (error) {
                showMessage('signupMessage', 'Network error occurred', 'error');
                debugLog(`Signup error: ${error.message}`, 'error');
            }
        });

        // Sign In Handler
        document.getElementById('signinForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('signinUsername').value;
            const password = document.getElementById('signinPassword').value;

            debugLog(`Starting signin for user: ${username}`);

            showMessage('signinMessage', 'Signing in...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password
                    })
                });

                debugLog(`Signin response status: ${response.status}`);
                const data = await response.json();
                debugLog(`Signin response: ${JSON.stringify(data, null, 2)}`);

                if (response.ok && data.success) {
                    showMessage('signinMessage', 'Sign in successful! Redirecting...', 'success');
                    debugLog('Signin successful', 'success');
                    debugLog(`Session token: ${data.sessionToken.substring(0, 20)}...`);
                    
                    // Store token
                    localStorage.setItem('lesavot_token', data.sessionToken);
                    localStorage.setItem('lesavot_user', JSON.stringify(data.user));
                    
                    // Redirect to main app
                    setTimeout(() => {
                        window.location.href = 'text_stego.html';
                    }, 2000);
                    
                } else {
                    showMessage('signinMessage', data.message || 'Invalid credentials', 'error');
                    debugLog(`Signin failed: ${data.message}`, 'error');
                }

            } catch (error) {
                showMessage('signinMessage', 'Network error occurred', 'error');
                debugLog(`Signin error: ${error.message}`, 'error');
            }
        });

        // Diagnostics
        async function runDiagnostics() {
            debugLog('🔍 Running system diagnostics...');
            
            try {
                // Test health endpoint
                debugLog('Testing health endpoint...');
                const healthResponse = await fetch('http://localhost:3000/api/health');
                const healthData = await healthResponse.json();
                
                if (healthResponse.ok) {
                    debugLog(`Health check: OK (${healthData.status})`, 'success');
                    debugLog(`Users: ${healthData.users}, Sessions: ${healthData.sessions}`);
                } else {
                    debugLog('Health check: FAILED', 'error');
                }

                // Test API endpoints
                debugLog('Testing API endpoints...');
                const endpoints = [
                    `${API_BASE}/auth/signup`,
                    `${API_BASE}/auth/signin`
                ];

                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(endpoint, { method: 'OPTIONS' });
                        debugLog(`${endpoint}: ${response.status}`, response.ok ? 'success' : 'error');
                    } catch (error) {
                        debugLog(`${endpoint}: ERROR - ${error.message}`, 'error');
                    }
                }

                debugLog('Diagnostics complete', 'success');

            } catch (error) {
                debugLog(`Diagnostics error: ${error.message}`, 'error');
            }
        }

        function clearDebug() {
            document.getElementById('debugOutput').textContent = '';
        }

        // Auto-run diagnostics on load
        window.addEventListener('load', () => {
            setTimeout(runDiagnostics, 1000);
        });
    </script>
</body>
</html>
