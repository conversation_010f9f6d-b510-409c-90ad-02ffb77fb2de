/* LESAVOT - Multimodal Steganography Application
   Main Stylesheet */

:root {
    /* Blue color palette */
    --primary-dark: #1a237e;    /* Dark blue */
    --primary: #3f51b5;         /* Primary blue */
    --primary-light: #7986cb;   /* Light blue */
    --primary-lighter: #c5cae9; /* Very light blue */
    --accent: #03a9f4;          /* Accent blue */
    --text-on-dark: #ffffff;    /* White text for dark backgrounds */
    --text-primary: #212121;    /* Dark text for light backgrounds */
    --text-secondary: #757575;  /* Secondary text color */
    --divider: #bdbdbd;         /* Divider color */
    --error: #f44336;           /* Error color */
    --success: #4caf50;         /* Success color */
    --warning: #ff9800;         /* Warning color */
    --info: #2196f3;            /* Info color */
    --background: #f5f7fa;      /* Light background */
    --card-bg: #ffffff;         /* Card background */
    --shadow: rgba(0, 0, 0, 0.1); /* Shadow color */
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>o', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--background) 0%, var(--primary-lighter) 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    background-color: var(--primary);
    color: var(--text-on-dark);
    padding: 1rem 0;
    box-shadow: 0 2px 5px var(--shadow);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-on-dark);
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-icon {
    margin-right: 10px;
    font-size: 2rem;
}

/* Navigation */
.nav-links {
    display: flex;
    align-items: center;
}

.nav-links a {
    color: var(--text-on-dark);
    text-decoration: none;
    padding: 0.5rem 1rem;
    margin-left: 5px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-links a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.auth-btn {
    background-color: var(--accent);
    color: var(--text-on-dark);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    margin-left: 10px;
    transition: background-color 0.3s;
}

.auth-btn:hover {
    background-color: #0288d1;
}

/* Main content */
main {
    padding: 2rem 0;
}

/* Card styles */
.card {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 4px 6px var(--shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow);
}

.card-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--divider);
}

.card-title {
    color: var(--primary-dark);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

input[type="text"],
input[type="password"],
input[type="email"],
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--divider);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
textarea:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-lighter);
}

input[type="file"] {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

button, .btn {
    background-color: var(--primary);
    color: var(--text-on-dark);
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover, .btn:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--text-secondary);
}

.btn-secondary:hover {
    background-color: #616161;
}

/* Flash messages */
.flash-messages {
    margin-bottom: 1.5rem;
}

.flash-message {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.flash-message.error {
    background-color: #ffebee;
    color: var(--error);
    border: 1px solid #ffcdd2;
}

.flash-message.success {
    background-color: #e8f5e9;
    color: var(--success);
    border: 1px solid #c8e6c9;
}

.flash-message.warning {
    background-color: #fff8e1;
    color: var(--warning);
    border: 1px solid #ffecb3;
}

.flash-message.info {
    background-color: #e3f2fd;
    color: var(--info);
    border: 1px solid #bbdefb;
}

/* Hero section */
.hero {
    text-align: center;
    padding: 3rem 1rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--text-on-dark);
    border-radius: 8px;
    margin-bottom: 2rem;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 1.5rem;
    opacity: 0.9;
}

/* Feature cards */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

/* Two-column grid layout */
.two-column-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

/* User welcome section */
.user-welcome {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

/* Auth links */
.auth-links {
    text-align: center;
    margin-top: 1.5rem;
}

.auth-links a {
    color: var(--primary);
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Center card */
.center-card {
    max-width: 500px;
    margin: 2rem auto;
}

/* Footer */
footer {
    background-color: var(--primary-dark);
    color: var(--text-on-dark);
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links a {
    color: var(--text-on-dark);
    margin-left: 1rem;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-links a:hover {
    opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        padding: 1rem;
    }

    .nav-links {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .features {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        margin-top: 1rem;
    }

    .footer-links a {
        margin: 0 0.5rem;
    }
}
