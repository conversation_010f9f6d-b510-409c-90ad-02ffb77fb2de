/**
 * Magic Link Authentication Routes
 * 
 * Defines API endpoints for Magic Link authentication system.
 */

const express = require('express');
const router = express.Router();

// Import controllers and middleware
const magicLinkController = require('../controllers/magicLinkController');
const { protect, optionalAuth, requireVerified } = require('../middleware/magicLinkAuth');

/**
 * @route   POST /api/auth/signup/request
 * @desc    Request OTP for user signup
 * @access  Public
 */
router.post('/signup/request', magicLinkController.requestSignupMagicLink);

/**
 * @route   POST /api/auth/signin/request
 * @desc    Request OTP for user signin
 * @access  Public
 */
router.post('/signin/request', magicLinkController.requestSigninMagicLink);

/**
 * @route   POST /api/auth/verify
 * @desc    Verify OTP and authenticate user
 * @access  Public
 */
router.post('/verify', magicLinkController.verifyMagicLink);

/**
 * @route   GET /api/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', protect, magicLinkController.getCurrentUser);

/**
 * @route   PUT /api/auth/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile', protect, magicLinkController.updateProfile);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (client-side token invalidation)
 * @access  Private
 */
router.post('/logout', protect, magicLinkController.logout);

/**
 * @route   GET /api/auth/status
 * @desc    Check authentication status
 * @access  Public (optional auth)
 */
router.get('/status', optionalAuth, (req, res) => {
  res.status(200).json({
    status: 'success',
    data: {
      authenticated: !!req.user,
      user: req.user || null
    }
  });
});

module.exports = router;
