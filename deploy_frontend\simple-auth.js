/**
 * Simple Username/Password Authentication System for LESAVOT
 * No OTP - Direct signup and signin
 */

class SimpleAuth {
    constructor() {
        this.apiBaseUrl = (window.CONFIG && window.CONFIG.apiBaseUrl + '/v1') || 'http://localhost:3000/api/v1';
        this.currentUser = null;
        this.isInitialized = false;
        this.init();
    }

    /**
     * Initialize the authentication system
     */
    async init() {
        try {
            this.setupEventListeners();
            this.isInitialized = true;
            console.log('Simple Username/Password Authentication system initialized');
        } catch (error) {
            console.error('Failed to initialize auth:', error);
        }
    }

    /**
     * Setup event listeners for forms
     */
    setupEventListeners() {
        // Signup form
        const signupBtn = document.getElementById('signupBtn');
        if (signupBtn) {
            signupBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignUp();
            });
        }

        // Signin form
        const signinBtn = document.getElementById('signinBtn');
        if (signinBtn) {
            signinBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignIn();
            });
        }

        // Password strength meter
        const signupPassword = document.getElementById('signupPassword');
        if (signupPassword) {
            signupPassword.addEventListener('input', (e) => {
                const password = e.target.value;
                const strength = this.calculatePasswordStrength(password);
                this.updateStrengthMeter(strength);
            });
        }

        // Form switching
        const showSignUpBtn = document.getElementById('showSignUpBtn');
        const showSignInBtn = document.getElementById('showSignInBtn');
        
        if (showSignUpBtn) {
            showSignUpBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSignupForm();
            });
        }

        if (showSignInBtn) {
            showSignInBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSigninForm();
            });
        }
    }

    /**
     * Handle signup process
     */
    async handleSignUp() {
        try {
            const username = document.getElementById('signupUsername')?.value?.trim();
            const email = document.getElementById('signupEmail')?.value?.trim();
            const password = document.getElementById('signupPassword')?.value;
            const confirmPassword = document.getElementById('confirmPassword')?.value;
            const agreeTerms = document.getElementById('agreeTerms')?.checked;

            // Validation
            if (!username || username.length < 3) {
                this.showMessage('Username must be at least 3 characters long', 'error');
                return;
            }

            if (!email || !this.isValidEmail(email)) {
                this.showMessage('Please enter a valid email address', 'error');
                return;
            }

            if (!password || password.length < 6) {
                this.showMessage('Password must be at least 6 characters long', 'error');
                return;
            }

            if (password !== confirmPassword) {
                this.showMessage('Passwords do not match', 'error');
                return;
            }

            if (!agreeTerms) {
                this.showMessage('Please agree to the Terms of Service and Privacy Policy', 'error');
                return;
            }

            this.showLoading('Creating account...');

            const response = await fetch(`${this.apiBaseUrl}/auth/signup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    email,
                    password,
                    confirmPassword
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.showMessage('Account created successfully! Please sign in.', 'success');

                // Switch to sign in form
                this.showSigninForm();
                // Pre-fill username
                const signinUsername = document.getElementById('signinUsername');
                if (signinUsername) {
                    signinUsername.value = username;
                }
            } else {
                this.showMessage(data.message || 'Failed to create account', 'error');
            }

        } catch (error) {
            console.error('Sign up error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle signin process
     */
    async handleSignIn() {
        try {
            const username = document.getElementById('signinUsername')?.value?.trim();
            const password = document.getElementById('signinPassword')?.value;

            // Validation
            if (!username) {
                this.showMessage('Please enter your username', 'error');
                return;
            }

            if (!password) {
                this.showMessage('Please enter your password', 'error');
                return;
            }

            this.showLoading('Signing in...');

            const response = await fetch(`${this.apiBaseUrl}/auth/signin`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    password
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.currentUser = data.user;
                this.storeToken(data.sessionToken);

                this.showMessage('Sign in successful! Welcome back!', 'success');

                setTimeout(() => {
                    window.location.href = 'text_stego.html';
                }, 2000);

            } else {
                this.showMessage(data.message || 'Invalid credentials', 'error');
            }

        } catch (error) {
            console.error('Sign in error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Show signup form
     */
    showSignupForm() {
        const signupForm = document.getElementById('signupForm');
        const signinForm = document.getElementById('signinForm');
        
        if (signupForm && signinForm) {
            signupForm.classList.add('active');
            signinForm.classList.remove('active');
        }
    }

    /**
     * Show signin form
     */
    showSigninForm() {
        const signupForm = document.getElementById('signupForm');
        const signinForm = document.getElementById('signinForm');
        
        if (signupForm && signinForm) {
            signinForm.classList.add('active');
            signupForm.classList.remove('active');
        }
    }

    /**
     * Utility functions
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        const messageDiv = document.getElementById('authMessage');
        if (messageDiv) {
            messageDiv.textContent = message;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }

    showLoading(message = 'Loading...') {
        const loadingDiv = document.getElementById('loadingMessage');
        if (loadingDiv) {
            loadingDiv.textContent = message;
            loadingDiv.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingDiv = document.getElementById('loadingMessage');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    storeToken(token) {
        localStorage.setItem('lesavot_session_token', token);
    }

    getStoredToken() {
        return localStorage.getItem('lesavot_session_token');
    }

    removeStoredToken() {
        localStorage.removeItem('lesavot_session_token');
    }

    isAuthenticated() {
        return !!this.currentUser && !!this.getStoredToken();
    }

    /**
     * Calculate password strength (0-4)
     */
    calculatePasswordStrength(password) {
        if (!password) return 0;
        let strength = 0;
        if (password.length >= 8) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        return Math.min(strength, 4);
    }

    /**
     * Update password strength meter
     */
    updateStrengthMeter(strength) {
        const strengthSegments = document.querySelectorAll('.strength-segment');
        const strengthText = document.querySelector('.strength-text');

        if (!strengthSegments.length || !strengthText) return;

        // Reset all segments
        strengthSegments.forEach(segment => {
            segment.className = 'strength-segment';
        });

        // Update segments based on strength
        for (let i = 0; i < strength; i++) {
            if (i < strengthSegments.length) {
                if (strength === 1) strengthSegments[i].classList.add('weak');
                else if (strength === 2) strengthSegments[i].classList.add('medium');
                else if (strength === 3) strengthSegments[i].classList.add('good');
                else if (strength === 4) strengthSegments[i].classList.add('strong');
            }
        }

        // Update strength text
        if (strength === 0) strengthText.textContent = 'Password strength';
        else if (strength === 1) strengthText.textContent = 'Weak password';
        else if (strength === 2) strengthText.textContent = 'Medium password';
        else if (strength === 3) strengthText.textContent = 'Good password';
        else strengthText.textContent = 'Strong password';
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

/**
 * Password toggle functionality
 */
function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.getElementById(inputId + 'Eye');
    
    if (passwordInput && eyeIcon) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.classList.remove('fa-eye');
            eyeIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            eyeIcon.classList.remove('fa-eye-slash');
            eyeIcon.classList.add('fa-eye');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.simpleAuth = new SimpleAuth();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleAuth;
}
