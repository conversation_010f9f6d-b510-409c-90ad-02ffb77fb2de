/**
 * LESAVOT - Simple Authentication System
 * 
 * This provides a simple authentication system that works with local storage
 * and can be extended to work with backend APIs.
 */

class UserAuth {
    constructor() {
        this.users = this.loadUsers();
        this.currentUser = null;
        this.isInitialized = false;
        this.init();
    }

    init() {
        // Initialize with default admin user if no users exist
        if (Object.keys(this.users).length === 0) {
            this.createDefaultAdmin();
        }
        this.isInitialized = true;
        console.log('UserAuth initialized successfully');
    }

    loadUsers() {
        try {
            const usersJson = localStorage.getItem('lesavot_users');
            return usersJson ? JSON.parse(usersJson) : {};
        } catch (error) {
            console.error('Error loading users:', error);
            return {};
        }
    }

    saveUsers() {
        try {
            localStorage.setItem('lesavot_users', JSON.stringify(this.users));
        } catch (error) {
            console.error('Error saving users:', error);
        }
    }

    createDefaultAdmin() {
        const defaultAdmin = {
            id: 'admin-' + Date.now(),
            username: 'admin',
            email: '<EMAIL>',
            fullName: 'LESAVOT Administrator',
            password: this.hashPassword('admin123'),
            isActive: true,
            createdAt: new Date().toISOString(),
            role: 'admin'
        };
        
        this.users[defaultAdmin.username] = defaultAdmin;
        this.saveUsers();
        console.log('Default admin user created: admin/admin123');
    }

    hashPassword(password) {
        // Simple hash function for demo purposes
        // In production, use proper bcrypt or similar
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    async register(userData) {
        const { username, email, password, fullName } = userData;
        
        // Check if user already exists
        if (this.users[username]) {
            throw new Error('Username already exists');
        }

        // Check if email already exists
        const existingUser = Object.values(this.users).find(user => user.email === email);
        if (existingUser) {
            throw new Error('Email already registered');
        }

        // Create new user
        const newUser = {
            id: 'user-' + Date.now(),
            username,
            email,
            fullName,
            password: this.hashPassword(password),
            isActive: true,
            createdAt: new Date().toISOString(),
            role: 'user'
        };

        this.users[username] = newUser;
        this.saveUsers();

        return {
            success: true,
            message: 'User registered successfully',
            user: {
                id: newUser.id,
                username: newUser.username,
                email: newUser.email,
                fullName: newUser.fullName
            }
        };
    }

    async login(username, password) {
        const user = this.users[username];
        if (!user) {
            throw new Error('User not found');
        }

        if (!user.isActive) {
            throw new Error('Account is disabled');
        }

        const hashedPassword = this.hashPassword(password);
        if (user.password !== hashedPassword) {
            throw new Error('Invalid password');
        }

        this.currentUser = user;
        localStorage.setItem('lesavot_current_user', JSON.stringify({
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName
        }));

        return {
            success: true,
            message: 'Login successful',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                fullName: user.fullName
            }
        };
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('lesavot_current_user');
        return { success: true, message: 'Logged out successfully' };
    }

    getCurrentUser() {
        if (this.currentUser) {
            return this.currentUser;
        }

        try {
            const userJson = localStorage.getItem('lesavot_current_user');
            if (userJson) {
                const userData = JSON.parse(userJson);
                this.currentUser = this.users[userData.username];
                return this.currentUser;
            }
        } catch (error) {
            console.error('Error getting current user:', error);
        }

        return null;
    }

    isLoggedIn() {
        return this.getCurrentUser() !== null;
    }

    getAllUsers() {
        return Object.values(this.users).map(user => ({
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            isActive: user.isActive,
            createdAt: user.createdAt,
            role: user.role
        }));
    }
}

// Create global instance
const userAuth = new UserAuth();

// Make it available globally
window.userAuth = userAuth;
window.UserAuth = UserAuth;

console.log('UserAuth system loaded successfully');
