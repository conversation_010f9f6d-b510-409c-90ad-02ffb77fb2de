# Master Deployment Script for LESAVOT Project
# Deploys both Frontend and Backend to their respective GitHub repositories

Write-Host "🚀 LESAVOT Master Deployment Script" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

# Function to display colored messages
function Write-ColoredOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Function to run deployment script
function Run-DeploymentScript {
    param (
        [Parameter(Mandatory=$true)]
        [string]$ScriptPath,
        [Parameter(Mandatory=$true)]
        [string]$ComponentName
    )
    
    Write-ColoredOutput "`n🔄 Starting $ComponentName deployment..." "Cyan"
    
    try {
        & powershell -ExecutionPolicy Bypass -File $ScriptPath
        if ($LASTEXITCODE -eq 0) {
            Write-ColoredOutput "✅ $ComponentName deployment completed successfully!" "Green"
            return $true
        } else {
            Write-ColoredOutput "❌ $ComponentName deployment failed!" "Red"
            return $false
        }
    } catch {
        Write-ColoredOutput "❌ Error running $ComponentName deployment: $_" "Red"
        return $false
    }
}

# Main deployment process
try {
    Write-ColoredOutput "🎯 LESAVOT Deployment Plan:" "Yellow"
    Write-ColoredOutput "1. Frontend → https://github.com/Bechi-cyber/LASAVOT" "Cyan"
    Write-ColoredOutput "2. Backend → https://github.com/Bechi-cyber/LASAVOT-Backend" "Cyan"
    Write-ColoredOutput ""

    # Ask for confirmation
    Write-ColoredOutput "Do you want to proceed with the deployment? (y/n): " "Yellow" -NoNewline
    $confirmation = Read-Host
    
    if ($confirmation -ne "y" -and $confirmation -ne "Y") {
        Write-ColoredOutput "❌ Deployment cancelled by user." "Red"
        exit 0
    }

    $deploymentResults = @{}
    
    # Deploy Frontend
    Write-ColoredOutput "`n" + "=" * 60 -ForegroundColor Yellow
    Write-ColoredOutput "📱 FRONTEND DEPLOYMENT" -ForegroundColor Yellow
    Write-ColoredOutput "=" * 60 -ForegroundColor Yellow
    
    $frontendSuccess = Run-DeploymentScript -ScriptPath "deploy-frontend.ps1" -ComponentName "Frontend"
    $deploymentResults["Frontend"] = $frontendSuccess
    
    # Deploy Backend
    Write-ColoredOutput "`n" + "=" * 60 -ForegroundColor Yellow
    Write-ColoredOutput "⚙️ BACKEND DEPLOYMENT" -ForegroundColor Yellow
    Write-ColoredOutput "=" * 60 -ForegroundColor Yellow
    
    $backendSuccess = Run-DeploymentScript -ScriptPath "deploy-backend.ps1" -ComponentName "Backend"
    $deploymentResults["Backend"] = $backendSuccess
    
    # Deployment Summary
    Write-ColoredOutput "`n" + "=" * 60 -ForegroundColor Yellow
    Write-ColoredOutput "📊 DEPLOYMENT SUMMARY" -ForegroundColor Yellow
    Write-ColoredOutput "=" * 60 -ForegroundColor Yellow
    
    foreach ($component in $deploymentResults.Keys) {
        $status = if ($deploymentResults[$component]) { "✅ SUCCESS" } else { "❌ FAILED" }
        $color = if ($deploymentResults[$component]) { "Green" } else { "Red" }
        Write-ColoredOutput "$component`: $status" $color
    }
    
    # Overall status
    $allSuccess = ($deploymentResults.Values | Where-Object { $_ -eq $false }).Count -eq 0
    
    if ($allSuccess) {
        Write-ColoredOutput "`n🎉 ALL DEPLOYMENTS COMPLETED SUCCESSFULLY!" "Green"
        Write-ColoredOutput "`n🔗 Repository Links:" "Cyan"
        Write-ColoredOutput "Frontend: https://github.com/Bechi-cyber/LASAVOT" "Cyan"
        Write-ColoredOutput "Backend:  https://github.com/Bechi-cyber/LASAVOT-Backend" "Cyan"
        Write-ColoredOutput "`n🌐 Live URLs:" "Cyan"
        Write-ColoredOutput "Frontend: https://lesavot.vercel.app" "Cyan"
        Write-ColoredOutput "Backend:  https://lasavot-backend.onrender.com" "Cyan"
    } else {
        Write-ColoredOutput "`n⚠️ Some deployments failed. Please check the logs above." "Yellow"
    }
    
} catch {
    Write-ColoredOutput "❌ Critical error during deployment process: $_" "Red"
    exit 1
}

Write-ColoredOutput "`nPress any key to exit..." "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
