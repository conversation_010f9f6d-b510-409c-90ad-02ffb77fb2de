// LESAVOT - Authentication Page JavaScript (with secure password hashing and advanced toggle)

// SHA-256 hashing utility
async function hashPassword(password) {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
    return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
}

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const signInForm = document.getElementById('signInForm');
    const signUpForm = document.getElementById('signUpForm');
    const showSignUpBtn = document.getElementById('showSignUpBtn');
    const showSignInBtn = document.getElementById('showSignInBtn');
    const signInBtn = document.getElementById('signInBtn');
    const signUpBtn = document.getElementById('signUpBtn');
    const signUpPassword = document.getElementById('signUpPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const strengthSegments = document.querySelectorAll('.strength-segment');
    const strengthText = document.querySelector('.strength-text');
    const signInPassword = document.getElementById('signInPassword');

    // OTP functionality removed for simplified authentication

    // Password visibility toggle state for each field
    const passwordToggleStates = {
        signInPassword: 0,
        signUpPassword: 0,
        confirmPassword: 0
    };

    // Secure, accessible password toggle logic
    function togglePasswordVisibility(input, toggleBtn, stateKey) {
        if (passwordToggleStates[stateKey] === 0) {
            input.type = 'text';
            toggleBtn.classList.remove('fa-eye');
            toggleBtn.classList.add('fa-eye-slash');
            toggleBtn.setAttribute('aria-label', 'Hide password');
            passwordToggleStates[stateKey] = 1;
        } else {
            input.type = 'password';
            toggleBtn.classList.remove('fa-eye-slash');
            toggleBtn.classList.add('fa-eye');
            toggleBtn.setAttribute('aria-label', 'Show password');
            passwordToggleStates[stateKey] = 0;
        }
    }

    // Password suggestion logic
    function generateStrongPassword() {
        // 20+ chars, mix of upper, lower, digits, symbols
        const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lower = 'abcdefghijklmnopqrstuvwxyz';
        const digits = '0123456789';
        const symbols = '!@#$%^&*()-_=+[]{}|;:,.<>?';
        const all = upper + lower + digits + symbols;
        let password = '';
        // Ensure at least one of each type
        password += upper[Math.floor(Math.random() * upper.length)];
        password += lower[Math.floor(Math.random() * lower.length)];
        password += digits[Math.floor(Math.random() * digits.length)];
        password += symbols[Math.floor(Math.random() * symbols.length)];
        for (let i = 4; i < 24; i++) {
            password += all[Math.floor(Math.random() * all.length)];
        }
        // Shuffle password
        password = password.split('').sort(() => 0.5 - Math.random()).join('');
        return password;
    }

    // Add suggest password button to signup form
    const suggestBtn = document.createElement('button');
    suggestBtn.type = 'button';
    suggestBtn.className = 'btn btn-outline password-suggest-btn';
    suggestBtn.textContent = 'Suggest Strong Password';
    const signUpPasswordGroup = signUpPassword.closest('.form-group');
    if (signUpPasswordGroup) {
        signUpPasswordGroup.appendChild(suggestBtn);
    }
    suggestBtn.addEventListener('click', function() {
        const strongPassword = generateStrongPassword();
        signUpPassword.value = strongPassword;
        confirmPassword.value = strongPassword;
        signUpPassword.dispatchEvent(new Event('input'));
        showNotification('A strong password has been suggested. You can use or edit it.', 'info');
    });

    // Password toggle event listeners (consistent, accessible, secure)
    const signInPasswordToggle = document.getElementById('signInPasswordToggle');
    if (signInPasswordToggle) {
        signInPasswordToggle.setAttribute('tabindex', '0');
        signInPasswordToggle.setAttribute('role', 'button');
        signInPasswordToggle.setAttribute('aria-label', 'Show password');
        signInPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(signInPassword, this, 'signInPassword');
        });
        signInPasswordToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility(signInPassword, this, 'signInPassword');
            }
        });
    }
    const signUpPasswordToggle = document.getElementById('signUpPasswordToggle');
    if (signUpPasswordToggle) {
        signUpPasswordToggle.setAttribute('tabindex', '0');
        signUpPasswordToggle.setAttribute('role', 'button');
        signUpPasswordToggle.setAttribute('aria-label', 'Show password');
        signUpPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(signUpPassword, this, 'signUpPassword');
        });
        signUpPasswordToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility(signUpPassword, this, 'signUpPassword');
            }
        });
    }
    const confirmPasswordToggle = document.getElementById('confirmPasswordToggle');
    if (confirmPasswordToggle) {
        confirmPasswordToggle.setAttribute('tabindex', '0');
        confirmPasswordToggle.setAttribute('role', 'button');
        confirmPasswordToggle.setAttribute('aria-label', 'Show password');
        confirmPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(confirmPassword, this, 'confirmPassword');
        });
        confirmPasswordToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility(confirmPassword, this, 'confirmPassword');
            }
        });
    }

    // Toggle between sign in and sign up forms
    showSignUpBtn.addEventListener('click', function(e) {
        e.preventDefault();
        signInForm.classList.remove('active');
        signUpForm.classList.add('active');
        // Clear signup fields
        document.getElementById('signUpFullName').value = '';
        document.getElementById('signUpUsername').value = '';
        document.getElementById('signUpEmail').value = '';
        document.getElementById('signUpPassword').value = '';
        document.getElementById('confirmPassword').value = '';
    });

    // On page load, ensure signup fields are empty
    document.getElementById('signUpFullName').value = '';
    document.getElementById('signUpUsername').value = '';
    document.getElementById('signUpEmail').value = '';
    document.getElementById('signUpPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    showSignInBtn.addEventListener('click', function(e) {
        e.preventDefault();
        signUpForm.classList.remove('active');
        signInForm.classList.add('active');
        // Clear sign in fields
        document.getElementById('signInUsername').value = '';
        document.getElementById('signInPassword').value = '';
    });

    // On page load, ensure sign in fields are empty
    document.getElementById('signInUsername').value = '';
    document.getElementById('signInPassword').value = '';

    // Autofill + fingerprint for password field
    const signInUsername = document.getElementById('signInUsername');
    const signInPasswordInput = document.getElementById('signInPassword');
    let autofillFingerprintRequested = false;
    signInPasswordInput.addEventListener('input', async function(e) {
        // Detect autofill: if both username and password are filled quickly, likely autofill
        if (
            signInUsername.value &&
            signInPasswordInput.value &&
            !autofillFingerprintRequested &&
            (document.activeElement !== signInPasswordInput)
        ) {
            autofillFingerprintRequested = true;
            // Prompt for fingerprint authentication (WebAuthn)
            if (window.PublicKeyCredential) {
                try {
                    await navigator.credentials.get({ publicKey: { challenge: new Uint8Array(32), timeout: 60000, userVerification: 'required' } });
                    showNotification('Fingerprint verified. You may now sign in.', 'success');
                } catch (e) {
                    showNotification('Fingerprint authentication failed or was cancelled.', 'error');
                }
            }
        }
    });
    signInPasswordInput.addEventListener('keydown', function() {
        autofillFingerprintRequested = false;
    });

    // Password strength meter
    signUpPassword.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updateStrengthMeter(strength);
    });

    // Password visibility toggles
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const passwordInput = this.parentElement.querySelector('input[type="password"], input[type="text"]');
            const isPassword = passwordInput.type === 'password';

            passwordInput.type = isPassword ? 'text' : 'password';
            this.classList.toggle('fa-eye');
            this.classList.toggle('fa-eye-slash');
        });
    });

    // Calculate password strength (0-4)
    function calculatePasswordStrength(password) {
        if (!password) return 0;
        let strength = 0;
        if (password.length >= 8) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        return Math.min(strength, 4);
    }
    function updateStrengthMeter(strength) {
        strengthSegments.forEach(segment => { segment.className = 'strength-segment'; });
        for (let i = 0; i < strength; i++) {
            if (i < strengthSegments.length) {
                if (strength === 1) strengthSegments[i].classList.add('weak');
                else if (strength === 2) strengthSegments[i].classList.add('medium');
                else if (strength === 3) strengthSegments[i].classList.add('good');
                else if (strength === 4) strengthSegments[i].classList.add('strong');
            }
        }
        if (strength === 0) strengthText.textContent = 'Password strength';
        else if (strength === 1) strengthText.textContent = 'Weak password';
        else if (strength === 2) strengthText.textContent = 'Medium password';
        else if (strength === 3) strengthText.textContent = 'Good password';
        else strengthText.textContent = 'Strong password';
    }

    // Sign Up button click handler
    signUpBtn.addEventListener('click', async function() {
        const fullName = document.getElementById('signUpFullName').value.trim();
        const username = document.getElementById('signUpUsername').value.trim();
        const email = document.getElementById('signUpEmail').value.trim();
        const password = document.getElementById('signUpPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const agreeTerms = document.getElementById('agreeTerms').checked;

        // Validation
        if (!fullName) {
            showNotification('Please enter your full name', 'error');
            return;
        }

        if (!username) {
            showNotification('Please enter a username', 'error');
            return;
        }

        if (!email) {
            showNotification('Please enter your email address', 'error');
            return;
        }

        if (!isValidEmail(email)) {
            showNotification('Please enter a valid email address', 'error');
            return;
        }

        if (!password) {
            showNotification('Please enter a password', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showNotification('Passwords do not match', 'error');
            return;
        }

        if (!agreeTerms) {
            showNotification('Please agree to the Terms of Service and Privacy Policy', 'error');
            return;
        }

        showNotification('Creating your account...', 'info');
        try {
            console.log('[AUTH DEBUG] Starting signup request', { username, email });

            const response = await fetch('http://localhost:3000/api/auth/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fullName,
                    username,
                    email,
                    password // Send plain password - backend will handle hashing
                })
            });

            console.log('[AUTH DEBUG] Signup response status:', response.status);
            const data = await response.json();
            console.log('[AUTH DEBUG] Signup response data:', data);

            if (response.ok && data.success) {
                showNotification('Account created successfully! You are now logged in.', 'success');

                // Store tokens if provided (auto-login after signup)
                if (data.token) {
                    localStorage.setItem('jwt_token', data.token);
                    console.log('[AUTH DEBUG] JWT token stored after signup');
                }
                if (data.refreshToken) {
                    localStorage.setItem('refresh_token', data.refreshToken);
                    console.log('[AUTH DEBUG] Refresh token stored after signup');
                }

                // Store user info
                if (data.user) {
                    localStorage.setItem('user_info', JSON.stringify(data.user));
                    localStorage.setItem('username', data.user.username);
                    console.log('[AUTH DEBUG] User info stored after signup');
                }

                console.log('[AUTH DEBUG] Redirecting to steganography page after signup');
                setTimeout(() => { window.location.href = 'text_stego.html'; }, 1000);
            } else {
                console.log('[AUTH DEBUG] Signup failed:', data.message);
                showNotification(data.message || 'Failed to create account', 'error');
            }
        } catch (error) {
            console.error('[AUTH DEBUG] Signup error:', error);
            showNotification('An error occurred during signup. Please try again.', 'error');
        }
    });

    // Sign In button click handler
    signInBtn.addEventListener('click', async function() {
        const username = document.getElementById('signInUsername').value.trim();
        const password = document.getElementById('signInPassword').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!username) {
            showNotification('Please enter your username', 'error');
            return;
        }

        if (!password) {
            showNotification('Please enter your password', 'error');
            return;
        }

        showNotification('Verifying credentials...', 'info');
        try {
            console.log('[AUTH DEBUG] Starting login request', { username });

            const response = await fetch('http://localhost:3000/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    password // Send plain password - backend will handle hashing
                })
            });

            console.log('[AUTH DEBUG] Login response status:', response.status);
            const data = await response.json();
            console.log('[AUTH DEBUG] Login response data:', data);

            if (response.ok && data.success) {
                // Simplified login - no OTP required
                showNotification('Sign in successful!', 'success');

                // Store tokens
                if (data.token) {
                    localStorage.setItem('jwt_token', data.token);
                    console.log('[AUTH DEBUG] JWT token stored');
                }
                if (data.refreshToken) {
                    localStorage.setItem('refresh_token', data.refreshToken);
                    console.log('[AUTH DEBUG] Refresh token stored');
                }

                // Remember user if checkbox is checked
                if (rememberMe) {
                    localStorage.setItem('rememberUser', 'true');
                    localStorage.setItem('username', username);
                    console.log('[AUTH DEBUG] User remembered');
                } else {
                    localStorage.removeItem('rememberUser');
                    localStorage.removeItem('username');
                }

                // Store user info
                if (data.user) {
                    localStorage.setItem('user_info', JSON.stringify(data.user));
                    console.log('[AUTH DEBUG] User info stored');
                }

                console.log('[AUTH DEBUG] Redirecting to steganography page');
                setTimeout(() => { window.location.href = 'text_stego.html'; }, 1000);
            } else {
                console.log('[AUTH DEBUG] Login failed:', data.message);
                showNotification(data.message || 'Invalid credentials', 'error');
            }
        } catch (error) {
            console.error('[AUTH DEBUG] Signin error:', error);
            showNotification('An error occurred during sign in. Please try again.', 'error');
        }
    });

    // OTP modal functionality removed for simplified authentication

    // OTP submission functionality removed for simplified authentication

    // Helper functions (showNotification, isValidEmail, handleSuccessfulLogin, etc.)
    function showNotification(message, type = 'info') {
        const notificationArea = document.getElementById('notificationArea');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        const notificationContent = document.createElement('div');
        notificationContent.className = 'notification-content';
        const header = document.createElement('div');
        header.className = 'notification-header';
        header.innerHTML = `<i class="fas fa-${icon}"></i><span class="notification-title">${type.charAt(0).toUpperCase() + type.slice(1)}</span>`;
        const messageElement = document.createElement('p');
        messageElement.className = 'notification-message';
        messageElement.textContent = message;
        notificationContent.appendChild(header);
        notificationContent.appendChild(messageElement);
        notification.appendChild(notificationContent);
        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        notification.appendChild(closeBtn);
        notificationArea.appendChild(notification);
        closeBtn.addEventListener('click', function() {
            notification.style.opacity = '0';
            setTimeout(() => { notification.remove(); }, 300);
        });
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                setTimeout(() => { if (notification.parentNode) notification.remove(); }, 300);
            }
        }, 5000);
    }
    function isValidEmail(email) {
        const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return re.test(email);
    }
    function handleSuccessfulLogin(user, rememberMe) {
        const displayName = user.fullName || user.username;
        showNotification(`Welcome back, ${displayName}!`, 'success');
        if (rememberMe) showNotification('Login credentials will be remembered', 'info');
        localStorage.setItem('username', user.username);
        if (rememberMe) localStorage.setItem('rememberUser', 'true');
        setTimeout(() => { window.location.href = 'text_stego.html'; }, 1000);
    }

    // Forgot Password functionality removed for simplified authentication

    // Forgot password modal functionality removed for simplified authentication
});
