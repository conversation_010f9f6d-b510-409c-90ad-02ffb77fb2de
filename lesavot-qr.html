<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT QR Code</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            text-align: center;
        }
        
        h1 {
            color: #1e2a3a;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        p {
            margin: 20px 0;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .qr-container {
            margin: 30px auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: inline-block;
        }
        
        .qr-code {
            margin: 0 auto;
        }
        
        .instructions {
            background-color: #e8f4fc;
            padding: 15px;
            border-radius: 5px;
            margin-top: 30px;
            text-align: left;
        }
        
        .instructions h2 {
            color: #2980b9;
            margin-top: 0;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <h1>LESAVOT Multimodal Steganography</h1>
    <p>Scan this QR code to access the LESAVOT application on your mobile device</p>
    
    <div class="qr-container">
        <img class="qr-code" src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://bechi-cyber.github.io/FINAL-LESAVOT/" alt="QR Code for LESAVOT">
        <p><a href="https://bechi-cyber.github.io/FINAL-LESAVOT/" target="_blank">https://bechi-cyber.github.io/FINAL-LESAVOT/</a></p>
    </div>
    
    <div class="instructions">
        <h2>How to use:</h2>
        <ol>
            <li>Open the camera app on your smartphone</li>
            <li>Point it at the QR code above</li>
            <li>Tap on the notification that appears</li>
            <li>The LESAVOT application will open in your browser</li>
        </ol>
    </div>
</body>
</html>
