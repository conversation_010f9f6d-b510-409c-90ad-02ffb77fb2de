const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

class OTPService {
    constructor() {
        this.otpStorage = new Map(); // In production, use Redis or database
        this.otpExpiry = 10 * 60 * 1000; // 10 minutes
        this.maxAttempts = 3;
    }

    /**
     * Generate a 6-digit OTP
     */
    generateOTP() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    /**
     * Generate and send OTP for email verification
     */
    async sendOTP(email, type = 'signup') {
        try {
            const otp = this.generateOTP();
            const expiresAt = Date.now() + this.otpExpiry;
            
            // Store OTP with metadata
            const otpData = {
                otp,
                email,
                type,
                expiresAt,
                attempts: 0,
                createdAt: Date.now()
            };
            
            this.otpStorage.set(email, otpData);
            
            // Send OTP via email
            await this.sendOTPEmail(email, otp, type);
            
            logger.info(`OTP generated for ${email} (${type})`);
            
            return {
                success: true,
                expiresAt,
                message: 'OTP sent successfully'
            };
        } catch (error) {
            logger.error('Error sending OTP:', error);
            throw new Error('Failed to send OTP');
        }
    }

    /**
     * Verify OTP
     */
    async verifyOTP(email, otp) {
        try {
            const otpData = this.otpStorage.get(email);
            
            if (!otpData) {
                return {
                    success: false,
                    message: 'OTP not found or expired'
                };
            }
            
            // Check if OTP is expired
            if (Date.now() > otpData.expiresAt) {
                this.otpStorage.delete(email);
                return {
                    success: false,
                    message: 'OTP has expired'
                };
            }
            
            // Check attempts
            if (otpData.attempts >= this.maxAttempts) {
                this.otpStorage.delete(email);
                return {
                    success: false,
                    message: 'Maximum verification attempts exceeded'
                };
            }
            
            // Verify OTP
            if (otpData.otp !== otp) {
                otpData.attempts++;
                this.otpStorage.set(email, otpData);
                return {
                    success: false,
                    message: `Invalid OTP. ${this.maxAttempts - otpData.attempts} attempts remaining`
                };
            }
            
            // OTP is valid - remove from storage
            this.otpStorage.delete(email);
            
            logger.info(`OTP verified successfully for ${email}`);
            
            return {
                success: true,
                message: 'OTP verified successfully',
                type: otpData.type
            };
        } catch (error) {
            logger.error('Error verifying OTP:', error);
            throw new Error('Failed to verify OTP');
        }
    }

    /**
     * Send OTP email
     */
    async sendOTPEmail(email, otp, type) {
        const subject = type === 'signup' ? 'Complete Your LESAVOT Signup' : 'LESAVOT Sign In Verification';
        
        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>${subject}</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
                    .content { padding: 30px; background: #f8f9fa; }
                    .otp-code { 
                        font-size: 32px; 
                        font-weight: bold; 
                        color: #e74c3c; 
                        text-align: center; 
                        padding: 20px; 
                        background: white; 
                        border: 2px dashed #e74c3c; 
                        margin: 20px 0; 
                        letter-spacing: 5px;
                    }
                    .warning { color: #e67e22; font-size: 14px; margin-top: 20px; }
                    .footer { text-align: center; padding: 20px; color: #7f8c8d; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔒 LESAVOT</h1>
                        <p>Secure Steganography Platform</p>
                    </div>
                    <div class="content">
                        <h2>Your Verification Code</h2>
                        <p>Hello,</p>
                        <p>Use the following verification code to ${type === 'signup' ? 'complete your signup' : 'sign in to your account'}:</p>
                        
                        <div class="otp-code">${otp}</div>
                        
                        <p><strong>This code will expire in 10 minutes.</strong></p>
                        
                        <div class="warning">
                            <p><strong>Security Notice:</strong></p>
                            <ul>
                                <li>Never share this code with anyone</li>
                                <li>LESAVOT will never ask for this code via phone or email</li>
                                <li>If you didn't request this code, please ignore this email</li>
                            </ul>
                        </div>
                    </div>
                    <div class="footer">
                        <p>This is an automated message from LESAVOT. Please do not reply to this email.</p>
                        <p>&copy; 2025 LESAVOT - Secure Steganography Platform</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        const textContent = `
LESAVOT - ${subject}

Your verification code: ${otp}

This code will expire in 10 minutes.

Security Notice:
- Never share this code with anyone
- LESAVOT will never ask for this code via phone or email
- If you didn't request this code, please ignore this email

This is an automated message from LESAVOT.
        `;

        // In development, log the OTP to console since email service is not configured
        logger.warn(`📧 EMAIL SERVICE NOT CONFIGURED - OTP for ${email}: ${otp}`);
        logger.warn(`📧 Subject: ${subject}`);
        logger.warn(`📧 Action: ${type === 'signup' ? 'Complete Signup' : 'Sign In'}`);
        logger.warn(`📧 In production, this would be sent via email.`);

        // Simulate successful email sending
        logger.info(`OTP email simulated for ${email}`);
    }

    /**
     * Generate session token after successful verification
     */
    generateSessionToken(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
            aud: 'lesavot-users',
            iss: 'lesavot-auth'
        };

        return jwt.sign(payload, process.env.JWT_SECRET);
    }

    /**
     * Clean up expired OTPs (should be called periodically)
     */
    cleanupExpiredOTPs() {
        const now = Date.now();
        for (const [email, otpData] of this.otpStorage.entries()) {
            if (now > otpData.expiresAt) {
                this.otpStorage.delete(email);
                logger.info(`Cleaned up expired OTP for ${email}`);
            }
        }
    }

    /**
     * Get OTP statistics (for monitoring)
     */
    getStats() {
        const now = Date.now();
        let active = 0;
        let expired = 0;

        for (const [email, otpData] of this.otpStorage.entries()) {
            if (now > otpData.expiresAt) {
                expired++;
            } else {
                active++;
            }
        }

        return {
            active,
            expired,
            total: this.otpStorage.size
        };
    }
}

// Cleanup expired OTPs every 5 minutes
const otpService = new OTPService();
setInterval(() => {
    otpService.cleanupExpiredOTPs();
}, 5 * 60 * 1000);

module.exports = otpService;
