/* LESAVOT - Audio Steganography
   Additional styles specific to audio steganography */

/* File upload styling */
.file-upload-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.file-input {
    position: absolute;
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    z-index: -1;
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    padding: 0.625rem 1rem;
    background-color: var(--primary-blue);
    color: var(--text-white);
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    font-size: 0.875rem;
}

.file-upload-label i {
    margin-right: 0.5rem;
}

.file-upload-label:hover {
    background-color: var(--primary-hover);
}

.file-name {
    color: var(--text-medium);
    font-size: 0.875rem;
}

/* Audio player styling */
.audio-player-container {
    margin-top: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    max-width: 100%;
    background-color: var(--lightest-blue);
    padding: 1rem;
}

.audio-player-container audio {
    width: 100%;
    margin-bottom: 0.5rem;
}

/* Waveform styling */
.waveform-container {
    height: 80px;
    background-color: var(--text-white);
    border-radius: 0.25rem;
    overflow: hidden;
    position: relative;
}

.waveform-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        var(--primary-blue) 0%, 
        var(--primary-hover) 50%, 
        var(--primary-blue) 100%
    );
    opacity: 0.1;
    z-index: 1;
}

/* Audio result styling */
.audio-result-container {
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    max-width: 100%;
    background-color: var(--lightest-blue);
    padding: 1rem;
}

.audio-result-container audio {
    width: 100%;
    margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .file-upload-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .file-name {
        margin-top: 0.5rem;
    }
}
