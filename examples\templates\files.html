<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Files - Multimodal Steganography</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .nav-links a {
            margin-left: 15px;
            color: #555;
            text-decoration: none;
        }
        .nav-links a:hover {
            color: #333;
            text-decoration: underline;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .flash-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .flash-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .file-type {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            color: white;
        }
        .file-type.image {
            background-color: #3498db;
        }
        .file-type.text {
            background-color: #e74c3c;
        }
        .file-type.audio {
            background-color: #9b59b6;
        }
        .action-btn {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 6px 12px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .action-btn:hover {
            background-color: #45a049;
        }
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #777;
        }
        .empty-state p {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header>
        <h1>My Steganographic Files</h1>
        <div class="nav-links">
            <a href="{{ url_for('dashboard') }}">Dashboard</a>
            <a href="{{ url_for('logout') }}">Logout</a>
        </div>
    </header>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    {% if files %}
        <table>
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Filename</th>
                    <th>Date Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for file in files %}
                <tr>
                    <td>
                        <span class="file-type {{ file.type }}">
                            {{ file.type.capitalize() }}
                        </span>
                    </td>
                    <td>{{ file.filename }}</td>
                    <td>{{ file.date }}</td>
                    <td>
                        <a href="{{ url_for('download', filename=file.filename) }}" class="action-btn">Download</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="empty-state">
            <p>You don't have any steganographic files yet.</p>
            <p>Go to one of the steganography tools to create your first hidden message!</p>
            <div>
                <a href="{{ url_for('dashboard') }}" class="action-btn">Go to Dashboard</a>
            </div>
        </div>
    {% endif %}
</body>
</html>
