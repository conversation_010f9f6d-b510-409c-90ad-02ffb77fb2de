const jwt = require('jsonwebtoken');
const OTPUser = require('../models/OTPUser');
const logger = require('../utils/logger');

/**
 * Middleware to protect routes with OTP authentication
 */
const protect = async (req, res, next) => {
    try {
        let token;

        // Get token from header
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        // Check if token exists
        if (!token) {
            return res.status(401).json({
                status: 'error',
                message: 'Access denied. No token provided.',
                isAuthenticated: false
            });
        }

        try {
            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Check token structure
            if (!decoded.userId || !decoded.email) {
                throw new Error('Invalid token structure');
            }

            // Check token audience and issuer
            if (decoded.aud !== 'lesavot-users' || decoded.iss !== 'lesavot-auth') {
                throw new Error('Invalid token audience or issuer');
            }

            // Get user from database
            const user = await OTPUser.findById(decoded.userId);
            
            if (!user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'User not found',
                    isAuthenticated: false
                });
            }

            // Check if user is verified
            if (!user.isVerified) {
                return res.status(401).json({
                    status: 'error',
                    message: 'User not verified',
                    isAuthenticated: false
                });
            }

            // Check if account is locked
            if (user.isAccountLocked()) {
                return res.status(423).json({
                    status: 'error',
                    message: 'Account is temporarily locked',
                    isAuthenticated: false
                });
            }

            // Add user to request object
            req.user = {
                userId: user.id,
                email: user.email,
                username: user.username,
                displayName: user.displayName,
                isVerified: user.isVerified
            };

            logger.debug(`Authenticated user: ${user.email}`);
            next();

        } catch (jwtError) {
            logger.warn(`Session token verification failed: ${jwtError.message}`);
            
            // Handle specific JWT errors
            let message = 'Invalid or expired token';
            if (jwtError.name === 'TokenExpiredError') {
                message = 'Token has expired. Please sign in again.';
            } else if (jwtError.name === 'JsonWebTokenError') {
                message = 'Invalid token. Please sign in again.';
            }

            return res.status(401).json({
                status: 'error',
                message,
                isAuthenticated: false
            });
        }

    } catch (error) {
        logger.error('Authentication middleware error:', error.message);
        return res.status(500).json({
            status: 'error',
            message: 'Authentication failed',
            isAuthenticated: false
        });
    }
};

/**
 * Middleware to optionally authenticate (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
    try {
        let token;

        // Get token from header
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        // If no token, continue without authentication
        if (!token) {
            req.user = null;
            return next();
        }

        try {
            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Get user from database
            const user = await OTPUser.findById(decoded.userId);
            
            if (user && user.isVerified && !user.isAccountLocked()) {
                req.user = {
                    userId: user.id,
                    email: user.email,
                    username: user.username,
                    displayName: user.displayName,
                    isVerified: user.isVerified
                };
            } else {
                req.user = null;
            }

        } catch (jwtError) {
            // If token is invalid, continue without authentication
            req.user = null;
        }

        next();

    } catch (error) {
        logger.error('Optional authentication middleware error:', error.message);
        req.user = null;
        next();
    }
};

/**
 * Middleware to check if user is admin (for future use)
 */
const requireAdmin = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                status: 'error',
                message: 'Authentication required'
            });
        }

        // For now, we'll check if user email contains 'admin'
        // In production, you'd have a proper role system
        if (!req.user.email.includes('admin')) {
            return res.status(403).json({
                status: 'error',
                message: 'Admin access required'
            });
        }

        next();

    } catch (error) {
        logger.error('Admin middleware error:', error.message);
        return res.status(500).json({
            status: 'error',
            message: 'Authorization failed'
        });
    }
};

/**
 * Middleware to validate JWT token structure without database lookup
 */
const validateToken = (req, res, next) => {
    try {
        let token;

        // Get token from header
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        // Check if token exists
        if (!token) {
            return res.status(401).json({
                status: 'error',
                message: 'Access denied. No token provided.'
            });
        }

        try {
            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Add decoded token to request
            req.tokenData = decoded;
            next();

        } catch (jwtError) {
            return res.status(401).json({
                status: 'error',
                message: 'Invalid or expired token'
            });
        }

    } catch (error) {
        logger.error('Token validation error:', error.message);
        return res.status(500).json({
            status: 'error',
            message: 'Token validation failed'
        });
    }
};

module.exports = {
    protect,
    optionalAuth,
    requireAdmin,
    validateToken
};
