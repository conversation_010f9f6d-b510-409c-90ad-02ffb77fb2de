<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT Connectivity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0a192f;
            color: #64ffda;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #64ffda;
            border-radius: 5px;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .info { color: #ffff00; }
        button {
            background-color: #64ffda;
            color: #0a192f;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #4fd1c7;
        }
        pre {
            background-color: #1e2a3a;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>LESAVOT Frontend-Backend Connectivity Test</h1>
    
    <div class="test-section">
        <h2>Configuration Test</h2>
        <button onclick="testConfiguration()">Test Configuration</button>
        <div id="configResult"></div>
    </div>

    <div class="test-section">
        <h2>Backend Health Check</h2>
        <button onclick="testHealthCheck()">Test Health Endpoint</button>
        <div id="healthResult"></div>
    </div>

    <div class="test-section">
        <h2>CORS Test</h2>
        <button onclick="testCORS()">Test CORS</button>
        <div id="corsResult"></div>
    </div>

    <div class="test-section">
        <h2>API Client Test</h2>
        <button onclick="testApiClient()">Test API Client</button>
        <div id="apiClientResult"></div>
    </div>

    <script src="config.js"></script>
    <script src="api-client.js"></script>
    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<div class="${className}">${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function testConfiguration() {
            const resultDiv = document.getElementById('configResult');
            resultDiv.innerHTML = '';
            
            log('configResult', 'Testing configuration...', 'info');
            
            if (window.CONFIG) {
                log('configResult', 'Configuration loaded successfully', 'success');
                log('configResult', `Environment: ${window.CONFIG.environment}`, 'info');
                log('configResult', `API Base URL: ${window.CONFIG.apiBaseUrl}`, 'info');
                log('configResult', `Is Development: ${window.CONFIG.isDevelopment}`, 'info');
                log('configResult', `Is Production: ${window.CONFIG.isProduction}`, 'info');
                
                const testUrl = window.CONFIG.getApiUrl('health');
                log('configResult', `Test API URL: ${testUrl}`, 'info');
            } else {
                log('configResult', 'Configuration not loaded!', 'error');
            }
        }

        async function testHealthCheck() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '';
            
            log('healthResult', 'Testing backend health endpoint...', 'info');
            
            const apiUrl = window.CONFIG ? window.CONFIG.getApiUrl('health') : 'https://lasavot-backend.onrender.com/api/health';
            
            try {
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                if (response.ok) {
                    log('healthResult', 'Backend health check successful!', 'success');
                    log('healthResult', `Status: ${data.status}`, 'success');
                    log('healthResult', `Environment: ${data.environment}`, 'info');
                    log('healthResult', `Database Status: ${data.database.status}`, 'success');
                } else {
                    log('healthResult', `Health check failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log('healthResult', `Health check error: ${error.message}`, 'error');
            }
        }

        async function testCORS() {
            const resultDiv = document.getElementById('corsResult');
            resultDiv.innerHTML = '';
            
            log('corsResult', 'Testing CORS configuration...', 'info');
            
            const apiUrl = window.CONFIG ? window.CONFIG.getApiUrl('health') : 'https://lasavot-backend.onrender.com/api/health';
            
            try {
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    }
                });
                
                if (response.ok) {
                    log('corsResult', 'CORS test successful!', 'success');
                    log('corsResult', `Origin: ${window.location.origin}`, 'info');
                    log('corsResult', 'Backend accepts requests from this origin', 'success');
                } else {
                    log('corsResult', `CORS test failed: ${response.status}`, 'error');
                }
            } catch (error) {
                if (error.message.includes('CORS')) {
                    log('corsResult', 'CORS error detected!', 'error');
                    log('corsResult', 'Backend may not be configured to accept requests from this origin', 'error');
                }
                log('corsResult', `CORS test error: ${error.message}`, 'error');
            }
        }

        async function testApiClient() {
            const resultDiv = document.getElementById('apiClientResult');
            resultDiv.innerHTML = '';
            
            log('apiClientResult', 'Testing API client...', 'info');
            
            try {
                const apiClient = new ApiClient();
                log('apiClientResult', 'API client created successfully', 'success');
                log('apiClientResult', `Base URL: ${apiClient.baseUrl}`, 'info');
                
                // Test health endpoint through API client
                const health = await apiClient.checkHealth();
                log('apiClientResult', 'API client health check successful!', 'success');
                log('apiClientResult', `Response: ${JSON.stringify(health, null, 2)}`, 'info');
                
            } catch (error) {
                log('apiClientResult', `API client test error: ${error.message}`, 'error');
            }
        }

        // Auto-run configuration test on load
        window.addEventListener('load', () => {
            setTimeout(testConfiguration, 500);
        });
    </script>
</body>
</html>
