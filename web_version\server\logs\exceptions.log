{"date":"Sat May 24 2025 06:26:47 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","os":{"loadavg":[0,0,0],"uptime":202684.156},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16619,"external":2107326,"heapTotal":32485376,"heapUsed":17316312,"rss":55910400},"pid":42812,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","timestamp":"2025-05-24 06:26:47:2647","trace":[{"column":15,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js","function":null,"line":27,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js","function":null,"line":30,"method":null,"native":false}]}
{"date":"Sat May 24 2025 07:43:37 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","os":{"loadavg":[0,0,0],"uptime":207294.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16619,"external":2107326,"heapTotal":32485376,"heapUsed":17102200,"rss":58269696},"pid":71864,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","timestamp":"2025-05-24 07:43:37:4337","trace":[{"column":15,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js","function":null,"line":27,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js","function":null,"line":30,"method":null,"native":false}]}
{"date":"Fri Jun 27 2025 20:38:14 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","os":{"loadavg":[0,0,0],"uptime":1349813.859},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3203437,"heapTotal":13062144,"heapUsed":9115304,"rss":44871680},"pid":62676,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","timestamp":"2025-06-27 20:38:14:3814","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":31,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":315,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false}]}
{"date":"Fri Jun 27 2025 20:38:22 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","os":{"loadavg":[0,0,0],"uptime":1349822.187},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3203437,"heapTotal":13062144,"heapUsed":9321320,"rss":45068288},"pid":29156,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","timestamp":"2025-06-27 20:38:22:3822","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":31,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":315,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false}]}
{"date":"Fri Jun 27 2025 20:38:31 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","os":{"loadavg":[0,0,0],"uptime":1349830.734},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3203437,"heapTotal":13324288,"heapUsed":9306856,"rss":44974080},"pid":59580,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","timestamp":"2025-06-27 20:38:31:3831","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":31,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":315,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false}]}
{"date":"Fri Jun 27 2025 20:38:54 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","os":{"loadavg":[0,0,0],"uptime":1349854.109},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3203437,"heapTotal":13062144,"heapUsed":9312880,"rss":44974080},"pid":19040,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:5:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","timestamp":"2025-06-27 20:38:54:3854","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":31,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":315,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false}]}
{"date":"Fri Jun 27 2025 20:53:30 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nC:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31\n      throw new Error('DATABASE_URL or individual database connection parameters are required');\n      ^\n\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at [eval]:2:18\n    at runScriptInThisContext (node:internal/vm:209:10)","os":{"loadavg":[0,0,0],"uptime":1350730.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3252949,"heapTotal":12800000,"heapUsed":9202792,"rss":44756992},"pid":59344,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31\n      throw new Error('DATABASE_URL or individual database connection parameters are required');\n      ^\n\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:31:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:315:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at [eval]:2:18\n    at runScriptInThisContext (node:internal/vm:209:10)","timestamp":"2025-06-27 20:53:30:5330","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":31,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":315,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"[eval]","function":null,"line":2,"method":null,"native":false},{"column":10,"file":"node:internal/vm","function":"runScriptInThisContext","line":209,"method":null,"native":false}]}
{"date":"Fri Jun 27 2025 21:26:11 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1352690.546},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3323627,"heapTotal":33533952,"heapUsed":19683864,"rss":61022208},"pid":72536,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:26:11:2611","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:26:22 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1352702.296},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3323627,"heapTotal":33533952,"heapUsed":21179280,"rss":62255104},"pid":69412,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:26:22:2622","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:26:43 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1352722.75},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3323627,"heapTotal":33009664,"heapUsed":19233848,"rss":60694528},"pid":81584,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:26:43:2643","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:28:25 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1352824.609},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65667,"external":3323627,"heapTotal":33009664,"heapUsed":21332040,"rss":62849024},"pid":50040,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:28:25:2825","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:31:11 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1352990.734},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65667,"external":3323627,"heapTotal":33533952,"heapUsed":21322600,"rss":62152704},"pid":25584,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:31:11:3111","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:31:48 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Cannot read properties of null (reading 'verify')\nTypeError: Cannot read properties of null (reading 'verify')\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:47:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\auth.js:8:24)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","os":{"loadavg":[0,0,0],"uptime":1353027.5},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3326077,"heapTotal":31412224,"heapUsed":14725688,"rss":56872960},"pid":28332,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"TypeError: Cannot read properties of null (reading 'verify')\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:47:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\auth.js:8:24)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","timestamp":"2025-06-27 21:31:48:3148","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":24,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\auth.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false}]}
{"date":"Fri Jun 27 2025 21:34:07 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1353166.812},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3323619,"heapTotal":33796096,"heapUsed":20129744,"rss":60645376},"pid":19028,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:34:07:347","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:34:29 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1353189.062},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65667,"external":3323619,"heapTotal":33271808,"heapUsed":21187192,"rss":61280256},"pid":60448,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:34:29:3429","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Fri Jun 27 2025 21:34:57 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\[eval]"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\[eval]\nnode:internal/modules/cjs/loader:1148\n  throw err;\n  ^\n\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\[eval]\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","os":{"loadavg":[0,0,0],"uptime":1353216.812},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3372723,"heapTotal":33271808,"heapUsed":19103928,"rss":60145664},"pid":85300,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"node:internal/modules/cjs/loader:1148\n  throw err;\n  ^\n\nError: Cannot find module '../config/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\routes\\steganography.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js\n- C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\[eval]\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:8:14)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)","timestamp":"2025-06-27 21:34:57:3457","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1145,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":986,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":14,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js","function":null,"line":8,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false}]}
{"date":"Wed Jul 02 2025 03:18:46 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:304:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":1719438.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":82457,"external":3489417,"heapTotal":16912384,"heapUsed":15381104,"rss":54509568},"pid":63284,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:304:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:18:46:1846","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":304,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 03:45:14 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Failed to generate authentication token\nC:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:100\n    throw new Error('Failed to generate authentication token');\n    ^\n\nError: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:100:11)\n    at [eval]:1:187\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","os":{"loadavg":[0,0,0],"uptime":8266.984},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16691,"external":1748614,"heapTotal":11227136,"heapUsed":7805248,"rss":44670976},"pid":49696,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:100\n    throw new Error('Failed to generate authentication token');\n    ^\n\nError: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:100:11)\n    at [eval]:1:187\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:45:14:4514","trace":[{"column":11,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js","function":"exports.generateToken","line":100,"method":"generateToken","native":false},{"column":187,"file":"[eval]","function":null,"line":1,"method":null,"native":false},{"column":10,"file":"node:internal/vm","function":"runScriptInThisContext","line":209,"method":null,"native":false},{"column":14,"file":"node:internal/process/execution","function":null,"line":118,"method":null,"native":false},{"column":24,"file":"[eval]-wrapper","function":null,"line":6,"method":null,"native":false},{"column":62,"file":"node:internal/process/execution","function":"runScript","line":101,"method":null,"native":false},{"column":3,"file":"node:internal/process/execution","function":"evalScript","line":133,"method":null,"native":false},{"column":3,"file":"node:internal/main/eval_string","function":null,"line":51,"method":null,"native":false}]}
{"date":"Fri Jul 04 2025 03:46:20 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Failed to generate authentication token\nC:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:106\n    throw new Error('Failed to generate authentication token');\n    ^\n\nError: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:106:11)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","os":{"loadavg":[0,0,0],"uptime":8332.64},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16691,"external":1748614,"heapTotal":11227136,"heapUsed":7883056,"rss":44756992},"pid":37200,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:106\n    throw new Error('Failed to generate authentication token');\n    ^\n\nError: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:106:11)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:46:20:4620","trace":[{"column":11,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js","function":"exports.generateToken","line":106,"method":"generateToken","native":false},{"column":148,"file":"[eval]","function":null,"line":1,"method":null,"native":false},{"column":10,"file":"node:internal/vm","function":"runScriptInThisContext","line":209,"method":null,"native":false},{"column":14,"file":"node:internal/process/execution","function":null,"line":118,"method":null,"native":false},{"column":24,"file":"[eval]-wrapper","function":null,"line":6,"method":null,"native":false},{"column":62,"file":"node:internal/process/execution","function":"runScript","line":101,"method":null,"native":false},{"column":3,"file":"node:internal/process/execution","function":"evalScript","line":133,"method":null,"native":false},{"column":3,"file":"node:internal/main/eval_string","function":null,"line":51,"method":null,"native":false}]}
{"date":"Fri Jul 04 2025 03:47:47 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Failed to generate authentication token\nC:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:109\n    throw new Error('Failed to generate authentication token');\n    ^\n\nError: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:109:11)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","os":{"loadavg":[0,0,0],"uptime":8419.64},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16691,"external":1748614,"heapTotal":10702848,"heapUsed":7958704,"rss":44474368},"pid":44368,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:109\n    throw new Error('Failed to generate authentication token');\n    ^\n\nError: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:109:11)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:47:47:4747","trace":[{"column":11,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js","function":"exports.generateToken","line":109,"method":"generateToken","native":false},{"column":148,"file":"[eval]","function":null,"line":1,"method":null,"native":false},{"column":10,"file":"node:internal/vm","function":"runScriptInThisContext","line":209,"method":null,"native":false},{"column":14,"file":"node:internal/process/execution","function":null,"line":118,"method":null,"native":false},{"column":24,"file":"[eval]-wrapper","function":null,"line":6,"method":null,"native":false},{"column":62,"file":"node:internal/process/execution","function":"runScript","line":101,"method":null,"native":false},{"column":3,"file":"node:internal/process/execution","function":"evalScript","line":133,"method":null,"native":false},{"column":3,"file":"node:internal/main/eval_string","function":null,"line":51,"method":null,"native":false}]}
{"date":"Fri Jul 04 2025 04:01:52 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":9264.75},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":82639,"external":3537675,"heapTotal":16650240,"heapUsed":15202872,"rss":54255616},"pid":50880,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 04:01:52:152","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":306,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 04:02:19 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":9291.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":82779,"external":3489739,"heapTotal":16650240,"heapUsed":15052952,"rss":55177216},"pid":37292,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 04:02:19:219","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":306,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 04:02:28 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nC:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:35\n      throw new Error('DATABASE_URL or individual database connection parameters are required');\n      ^\n\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:35:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:339:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at [eval]:1:59\n    at runScriptInThisContext (node:internal/vm:209:10)","os":{"loadavg":[0,0,0],"uptime":9300.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3252949,"heapTotal":13062144,"heapUsed":9562112,"rss":48685056},"pid":50592,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:35\n      throw new Error('DATABASE_URL or individual database connection parameters are required');\n      ^\n\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:35:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:339:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at [eval]:1:59\n    at runScriptInThisContext (node:internal/vm:209:10)","timestamp":"2025-07-04 04:02:28:228","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":35,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":339,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":59,"file":"[eval]","function":null,"line":1,"method":null,"native":false},{"column":10,"file":"node:internal/vm","function":"runScriptInThisContext","line":209,"method":null,"native":false}]}
{"date":"Fri Jul 04 2025 04:04:57 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":9450.187},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":82639,"external":3489599,"heapTotal":16125952,"heapUsed":14766192,"rss":53882880},"pid":46104,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 04:04:57:457","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":306,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 04:15:46 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":10098.687},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":82779,"external":3489739,"heapTotal":16650240,"heapUsed":15202368,"rss":55476224},"pid":37316,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 04:15:46:1546","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":306,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 04:16:09 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":10121.406},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83058,"external":3490018,"heapTotal":16650240,"heapUsed":15008064,"rss":54190080},"pid":46200,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 04:16:09:169","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":306,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 04:31:05 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":11017.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83199,"external":3490159,"heapTotal":16650240,"heapUsed":15152080,"rss":54226944},"pid":48416,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 04:31:05:315","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":306,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"date":"Fri Jul 04 2025 11:35:19 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:40:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:344:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:10:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","os":{"loadavg":[0,0,0],"uptime":14688.14},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3326037,"heapTotal":33533952,"heapUsed":15266344,"rss":62287872},"pid":11032,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:40:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:344:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:10:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","timestamp":"2025-07-04 11:35:19:3519","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":40,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":344,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false}]}
{"date":"Mon Jul 07 2025 11:36:57 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3000\nError: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:368:12)","os":{"loadavg":[0,0,0],"uptime":273980.859},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16619,"external":2221174,"heapTotal":33533952,"heapUsed":14842240,"rss":62763008},"pid":45740,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:368:12)","timestamp":"2025-07-07 11:36:57:3657","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1904,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1961,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2063,"method":"listen","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":"startServer","line":368,"method":null,"native":false}]}
{"date":"Mon Jul 07 2025 18:00:35 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js:30:9)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js:1:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","os":{"loadavg":[0,0,0],"uptime":296998.984},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65666,"external":3326037,"heapTotal":33271808,"heapUsed":16765576,"rss":63340544},"pid":48204,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js:30:9)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js:1:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","timestamp":"2025-07-07 18:00:35:035","trace":[{"column":9,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false}]}
{"date":"Mon Jul 07 2025 18:07:34 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js:30:9)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js:1:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","os":{"loadavg":[0,0,0],"uptime":297418.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3230618,"heapTotal":20926464,"heapUsed":15078504,"rss":55287808},"pid":63880,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js:30:9)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js:1:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","timestamp":"2025-07-07 18:07:34:734","trace":[{"column":9,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false}]}
{"date":"Mon Jul 07 2025 18:25:59 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nError: DATABASE_URL or individual database connection parameters are required\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js:30:9)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js:1:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","os":{"loadavg":[0,0,0],"uptime":298522.953},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3230618,"heapTotal":20926464,"heapUsed":15057784,"rss":55664640},"pid":9852,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: DATABASE_URL or individual database connection parameters are required\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js:30:9)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js:1:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)","timestamp":"2025-07-07 18:25:59:2559","trace":[{"column":9,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\config\\database.js","function":null,"line":30,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\OTPUser.js","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false}]}
{"date":"Mon Jul 07 2025 18:47:10 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: authRoutes is not defined\nReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[0,0,0],"uptime":299794.64},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16659,"external":2092740,"heapTotal":20140032,"heapUsed":11688560,"rss":50614272},"pid":40276,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"ReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-07 18:47:10:4710","trace":[{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":null,"line":154,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":174,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
{"date":"Mon Jul 07 2025 19:02:36 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: authRoutes is not defined\nReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[0,0,0],"uptime":300720.39},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16659,"external":2092740,"heapTotal":20402176,"heapUsed":11679952,"rss":51212288},"pid":45932,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"ReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-07 19:02:36:236","trace":[{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":null,"line":154,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":174,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
{"date":"Mon Jul 07 2025 19:04:08 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: DATABASE_URL or individual database connection parameters are required\nC:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:40\n      throw new Error('DATABASE_URL or individual database connection parameters are required');\n      ^\n\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:40:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:344:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:10:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","os":{"loadavg":[0,0,0],"uptime":300812.75},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65706,"external":3359752,"heapTotal":18042880,"heapUsed":11427368,"rss":49561600},"pid":67256,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:40\n      throw new Error('DATABASE_URL or individual database connection parameters are required');\n      ^\n\nError: DATABASE_URL or individual database connection parameters are required\n    at new Database (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:40:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:344:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:10:18)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)","timestamp":"2025-07-07 19:04:08:48","trace":[{"column":13,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":"new Database","line":40,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js","function":null,"line":344,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":18,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js","function":null,"line":10,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false}]}
{"date":"Mon Jul 07 2025 19:06:21 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: authRoutes is not defined\nReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[0,0,0],"uptime":300945.406},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16659,"external":2092740,"heapTotal":20402176,"heapUsed":11744872,"rss":50941952},"pid":11036,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"ReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-07 19:06:21:621","trace":[{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":null,"line":154,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":174,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
{"date":"Mon Jul 07 2025 21:04:41 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: authRoutes is not defined\nReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[0,0,0],"uptime":308045.843},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16659,"external":2092740,"heapTotal":20140032,"heapUsed":11755696,"rss":52002816},"pid":47140,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"ReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-07 21:04:41:441","trace":[{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":null,"line":154,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":174,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
{"date":"Mon Jul 07 2025 21:33:49 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: authRoutes is not defined\nReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[0,0,0],"uptime":309793.625},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16659,"external":2092740,"heapTotal":19877888,"heapUsed":11678824,"rss":51183616},"pid":48064,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"ReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-07 21:33:49:3349","trace":[{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":null,"line":154,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":174,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
{"date":"Mon Jul 07 2025 21:37:47 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: authRoutes is not defined\nReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[0,0,0],"uptime":310030.875},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16659,"external":2092740,"heapTotal":20402176,"heapUsed":11651320,"rss":50769920},"pid":56628,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"ReferenceError: authRoutes is not defined\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:154:31)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:174:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-07 21:37:47:3747","trace":[{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js","function":null,"line":154,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":174,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
