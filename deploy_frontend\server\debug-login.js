/**
 * Debug Login Issues
 */

require('dotenv').config();
const User = require('./models/User');
const Session = require('./models/Session');
const Metrics = require('./models/Metrics');

async function debugLogin() {
  console.log('=== DEBUG LOGIN ISSUES ===');
  
  try {
    // Test finding user
    console.log('1. Testing User.findByUsername...');
    const user = await User.findByUsername('testuser2');
    if (user) {
      console.log('✅ User found:', user.username, user.email);
      console.log('   Password hash exists:', !!user.passwordHash);
      
      // Test password verification
      console.log('2. Testing password verification...');
      const isValid = await user.verifyPassword('TestPassword123!');
      console.log('   Password valid:', isValid);
      
      // Test account lock status
      console.log('3. Testing account lock status...');
      const isLocked = user.isAccountLocked();
      console.log('   Account locked:', isLocked);
      
    } else {
      console.log('❌ User not found');
    }
    
    // Test Session model
    console.log('4. Testing Session model...');
    try {
      const sessionTest = await Session.findActiveOtpSession('testuser2');
      console.log('   Session model working:', sessionTest === null ? 'No active session (expected)' : 'Active session found');
    } catch (error) {
      console.log('❌ Session model error:', error.message);
    }
    
    // Test Metrics model
    console.log('5. Testing Metrics model...');
    try {
      await Metrics.recordEvent('test_event', 1, 'debug', { test: true });
      console.log('✅ Metrics model working');
    } catch (error) {
      console.log('❌ Metrics model error:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
}

debugLogin().then(() => {
  console.log('=== DEBUG COMPLETE ===');
  process.exit(0);
}).catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
