/**
 * PWA Styles
 * 
 * This file contains styles for PWA-specific elements like the offline bar,
 * update notification, and install button.
 */

/* Offline Bar */
.offline-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px 20px;
    text-align: center;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.offline-bar span {
    flex: 1;
}

.offline-bar button {
    background: none;
    border: none;
    color: #721c24;
    font-size: 20px;
    cursor: pointer;
    padding: 0 10px;
}

/* Update Notification */
.update-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #d4edda;
    color: #155724;
    padding: 10px 20px;
    text-align: center;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.update-bar span {
    flex: 1;
}

.update-bar button {
    background-color: #155724;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 15px;
    cursor: pointer;
    font-weight: bold;
}

.update-bar button:hover {
    background-color: #0d3c17;
}

/* Install Button */
.install-button {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin: 10px;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.install-button:hover {
    background-color: #0056b3;
}

.install-button i {
    font-size: 16px;
}

/* Offline Mode Styles */
.offline-mode .online-only {
    opacity: 0.5;
    pointer-events: none;
}

.offline-mode .offline-notice {
    display: block;
}

.offline-notice {
    display: none;
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Offline Page Styles */
.offline-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    text-align: center;
    padding: 2rem;
    background-color: var(--bg-color, #0a192f);
    color: var(--text-color, #e6f1ff);
}

.offline-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: var(--accent-color, #64ffda);
}

.offline-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color, #64ffda);
}

.offline-message {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
}

.offline-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.offline-button {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color, #64ffda);
    color: var(--bg-color, #0a192f);
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.offline-button:hover {
    background-color: var(--primary-dark, #4db6ac);
}

.offline-features {
    margin-top: 3rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 900px;
}

.offline-feature {
    background-color: var(--card-bg, #112240);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.offline-feature h3 {
    color: var(--primary-color, #64ffda);
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .offline-title {
        font-size: 2rem;
    }
    
    .offline-message {
        font-size: 1rem;
    }
    
    .offline-features {
        grid-template-columns: 1fr;
    }
}
