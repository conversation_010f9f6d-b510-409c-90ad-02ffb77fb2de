{"date":"Sat May 24 2025 06:26:47 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","os":{"loadavg":[0,0,0],"uptime":202684.156},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16619,"external":2107326,"heapTotal":32485376,"heapUsed":17316312,"rss":55910400},"pid":42812,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","timestamp":"2025-05-24 06:26:47:2647","trace":[{"column":15,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js","function":null,"line":27,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js","function":null,"line":30,"method":null,"native":false}]}
{"date":"Sat May 24 2025 07:43:37 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","os":{"loadavg":[0,0,0],"uptime":207294.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js"],"cwd":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":16619,"external":2107326,"heapTotal":32485376,"heapUsed":17102200,"rss":58269696},"pid":71864,"uid":null,"version":"v20.16.0"},"service":"lesavot-api","stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js:27:8)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js:30:20)","timestamp":"2025-05-24 07:43:37:4337","trace":[{"column":15,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\node_modules\\express\\lib\\router\\index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\routes\\auth.js","function":null,"line":27,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1358,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1416,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1208,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1024,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1233,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":20,"file":"C:\\Users\\<USER>\\Desktop\\LESAVOT\\web_version\\server\\server.js","function":null,"line":30,"method":null,"native":false}]}
