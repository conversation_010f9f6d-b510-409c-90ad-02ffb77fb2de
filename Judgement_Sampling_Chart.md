# Judgement Sampling Distribution Chart

## Sampling Framework Overview

### Target Population: Academic Community
**Total Sample Size**: 38 participants
**Sampling Method**: Purposive (Judgement) Sampling
**Selection Criteria**: Academic background in relevant fields

## Sampling Distribution by Academic Program

### Primary Sampling Categories

| Category | Target % | Actual Count | Actual % | Justification |
|----------|----------|--------------|----------|---------------|
| Cybersecurity | 40% | 16 | 42.1% | Primary target users with security expertise |
| Computer Science | 15% | 4 | 10.5% | Technical background for algorithm evaluation |
| Information Systems | 10% | 3 | 7.9% | Systems integration perspective |
| Software Engineering | 10% | 3 | 7.9% | Implementation and usability insights |
| BMS Faculty | 25% | 12 | 31.6% | End-user perspective and educational applications |

## Visual Representation

### Sampling Distribution Bar Chart
```
Cybersecurity        ████████████████████████████████████████████ 42.1% (16)
                     Target: 40% ████████████████████████████████████████
                     
BMS Faculty          ████████████████████████████████████ 31.6% (12)
                     Target: 25% ███████████████████████████
                     
Computer Science     ███████████ 10.5% (4)
                     Target: 15% ████████████████
                     
Info Systems & Net   ████████ 7.9% (3)
                     Target: 10% ███████████
                     
Software Engineering ████████ 7.9% (3)
                     Target: 10% ███████████
                     
                     0    10    20    30    40    50%
```

### Expertise Level Distribution
```
Advanced Users       ████████████████████████████████████████ 39.5% (15)
Intermediate Users   ████████████████████████████████████████ 39.5% (15)
Beginner Users       █████████████████████ 21.1% (8)
                     0    10    20    30    40    50%
```

### Gender Representation
```
Male Participants    ████████████████████████████████████████████████████████████ 63.2% (24)
Female Participants  ████████████████████████████████████████ 36.8% (14)
                     0    10    20    30    40    50    60    70%
```

## Sampling Rationale

### Selection Criteria Applied

**Technical Expertise Requirements:**
- Understanding of encryption concepts
- Familiarity with digital security
- Experience with software applications
- Academic or professional background in relevant fields

**Diversity Considerations:**
- Multiple academic disciplines represented
- Range of technical expertise levels
- Gender diversity maintained
- Faculty and student perspectives included

**Sample Size Justification:**
- Sufficient for statistical analysis (n=38)
- Manageable for qualitative feedback collection
- Representative of target user population
- Adequate power for hypothesis testing

## Sampling Bias Mitigation

### Strategies Employed:
1. **Multi-departmental recruitment** - Avoided single-program bias
2. **Voluntary participation** - Reduced selection pressure
3. **Anonymous responses** - Encouraged honest feedback
4. **Diverse expertise levels** - Captured varied user perspectives
5. **Faculty inclusion** - Balanced student-heavy samples

### Limitations Acknowledged:
- Single institution sampling
- Self-selection bias in volunteers
- Technical background requirement
- Limited age range diversity

## Statistical Validity

### Sample Adequacy:
- **Minimum required**: 30 participants for normal distribution
- **Achieved**: 38 participants (126.7% of minimum)
- **Power analysis**: 80% power to detect medium effect sizes
- **Confidence level**: 95% for primary analyses

### Representativeness:
- **Target population**: Academic users of security software
- **Coverage**: Multiple relevant academic disciplines
- **Expertise range**: Beginner to advanced users
- **Application domains**: Education, research, professional use

## Recruitment Process

### Phase 1: Initial Contact (Week 1)
- Department liaison coordination
- Faculty permission obtained
- Student organization outreach
- Information sessions conducted

### Phase 2: Screening (Week 2)
- Eligibility criteria verification
- Informed consent collection
- Demographic data gathering
- Schedule coordination

### Phase 3: Participation (Weeks 3-4)
- Platform demonstration
- Hands-on testing sessions
- Survey completion
- Follow-up interviews (selected participants)

## Quality Assurance

### Data Collection Standards:
- Standardized testing environment
- Consistent instruction delivery
- Systematic data recording
- Regular quality checks

### Participant Engagement:
- Clear instructions provided
- Technical support available
- Flexible scheduling offered
- Incentives for completion
