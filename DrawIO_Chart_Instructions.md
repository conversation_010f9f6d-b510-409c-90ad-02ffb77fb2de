# Draw.io Chart Creation Instructions

## Chart 1: Demographics Table (Table 3.1)

### Table Setup:
- **Dimensions**: 4 columns × 6 rows
- **Cell size**: Width 150px, Height 35px
- **Font**: <PERSON><PERSON>, 12pt
- **Header**: Bold, Blue background (#4472C4), White text
- **Body**: White background, Black text

### Data to Input:
```
Row 1 (Header): Academic Program | Participants | Percentage | Gender (M/F)
Row 2: Cybersecurity | 16 | 42.1% | 10/6
Row 3: BMS Faculty | 12 | 31.6% | 7/5
Row 4: Computer Science | 4 | 10.5% | 3/1
Row 5: Info Systems & Networking | 3 | 7.9% | 2/1
Row 6: Software Engineering | 3 | 7.9% | 2/1
Row 7: TOTAL | 38 | 100.0% | 24/14
```

---

## Chart 2: Academic Program Bar Chart

### Bar Chart Setup:
- **Chart Type**: Horizontal Bar Chart
- **Canvas Size**: 600px × 400px
- **Bar Height**: 40px each
- **Spacing**: 10px between bars

### Bar Data (Left to Right):
1. **Cybersecurity**: 420px width (42.1%)
2. **BMS Faculty**: 316px width (31.6%)
3. **Computer Science**: 105px width (10.5%)
4. **Info Systems**: 79px width (7.9%)
5. **Software Engineering**: 79px width (7.9%)

### Colors:
- Cybersecurity: #1f77b4 (Blue)
- BMS Faculty: #ff7f0e (Orange)
- Computer Science: #2ca02c (Green)
- Info Systems: #d62728 (Red)
- Software Engineering: #9467bd (Purple)

### Labels:
- Y-axis: Program names
- X-axis: "Percentage of Participants"
- Title: "Participant Distribution by Academic Program"
- Data labels: Show percentage on each bar

---

## Chart 3: Survey Response Table (Table 4.1)

### Table Setup:
- **Dimensions**: 6 columns × 8 rows
- **Cell size**: Width 120px, Height 30px
- **Header**: Bold, Dark Blue (#2F4F4F), White text
- **Alternating rows**: Light gray (#F8F8F8) and white

### Column Headers:
```
Response Category | Very Poor | Poor | Average | Good | Excellent
```

### Data Rows:
```
Platform Usability | 2 (5.3%) | 3 (7.9%) | 8 (21.1%) | 15 (39.5%) | 10 (26.3%)
Security Confidence | 1 (2.6%) | 2 (5.3%) | 6 (15.8%) | 18 (47.4%) | 11 (28.9%)
Processing Speed | 3 (7.9%) | 4 (10.5%) | 9 (23.7%) | 14 (36.8%) | 8 (21.1%)
Feature Satisfaction | 1 (2.6%) | 3 (7.9%) | 7 (18.4%) | 16 (42.1%) | 11 (28.9%)
Educational Value | 0 (0.0%) | 2 (5.3%) | 5 (13.2%) | 17 (44.7%) | 14 (36.8%)
Overall Satisfaction | 1 (2.6%) | 2 (5.3%) | 6 (15.8%) | 19 (50.0%) | 10 (26.3%)
Recommendation | 2 (5.3%) | 3 (7.9%) | 7 (18.4%) | 16 (42.1%) | 10 (26.3%)
```

---

## Chart 4: Stacked Bar Chart - Response Distribution

### Setup:
- **Chart Type**: Horizontal Stacked Bar
- **Canvas Size**: 700px × 500px
- **Bar Height**: 50px each
- **Total Width**: 500px per bar (100%)

### Color Scheme:
- **Very Poor**: #d62728 (Red)
- **Poor**: #ff7f0e (Orange)
- **Average**: #ffbb78 (Light Orange)
- **Good**: #2ca02c (Green)
- **Excellent**: #1f77b4 (Blue)

### Bar Segments (Width in pixels for 500px total):

**Platform Usability:**
- Very Poor: 27px (5.3%)
- Poor: 40px (7.9%)
- Average: 106px (21.1%)
- Good: 198px (39.5%)
- Excellent: 132px (26.3%)

**Security Confidence:**
- Very Poor: 13px (2.6%)
- Poor: 27px (5.3%)
- Average: 79px (15.8%)
- Good: 237px (47.4%)
- Excellent: 145px (28.9%)

**Processing Speed:**
- Very Poor: 40px (7.9%)
- Poor: 53px (10.5%)
- Average: 119px (23.7%)
- Good: 184px (36.8%)
- Excellent: 106px (21.1%)

**Feature Satisfaction:**
- Very Poor: 13px (2.6%)
- Poor: 40px (7.9%)
- Average: 92px (18.4%)
- Good: 211px (42.1%)
- Excellent: 145px (28.9%)

**Educational Value:**
- Very Poor: 0px (0.0%)
- Poor: 27px (5.3%)
- Average: 66px (13.2%)
- Good: 224px (44.7%)
- Excellent: 184px (36.8%)

**Overall Satisfaction:**
- Very Poor: 13px (2.6%)
- Poor: 27px (5.3%)
- Average: 79px (15.8%)
- Good: 250px (50.0%)
- Excellent: 132px (26.3%)

**Recommendation:**
- Very Poor: 27px (5.3%)
- Poor: 40px (7.9%)
- Average: 92px (18.4%)
- Good: 211px (42.1%)
- Excellent: 132px (26.3%)

---

## Chart 5: Judgement Sampling Pie Chart

### Setup:
- **Chart Type**: Pie Chart
- **Canvas Size**: 500px × 500px
- **Radius**: 200px

### Segments (Degrees):
- **Cybersecurity**: 151.6° (42.1%)
- **BMS Faculty**: 113.8° (31.6%)
- **Computer Science**: 37.8° (10.5%)
- **Info Systems**: 28.4° (7.9%)
- **Software Engineering**: 28.4° (7.9%)

### Colors:
- Cybersecurity: #1f77b4 (Blue)
- BMS Faculty: #ff7f0e (Orange)
- Computer Science: #2ca02c (Green)
- Info Systems: #d62728 (Red)
- Software Engineering: #9467bd (Purple)

### Labels:
- **Title**: "Judgement Sampling Distribution"
- **Legend**: Show program names with percentages
- **Data Labels**: Show percentages on each slice

---

## Export Settings:

### For all charts:
- **Format**: PNG
- **Resolution**: 300 DPI
- **Background**: White
- **Border**: 2px solid gray
- **Transparent**: No

### File Names:
- `Table_3_1_Demographics.png`
- `Academic_Program_Distribution.png`
- `Table_4_1_Survey_Responses.png`
- `Response_Distribution_Stacked.png`
- `Judgement_Sampling_Pie.png`

---

## Quick Creation Tips:

1. **Use Templates**: Start with draw.io chart templates
2. **Copy Data**: Use copy-paste for table data
3. **Consistent Styling**: Use the same fonts and colors throughout
4. **Alignment**: Use draw.io's alignment tools
5. **Grouping**: Group related elements together
6. **Export Quality**: Always use high DPI for thesis inclusion
