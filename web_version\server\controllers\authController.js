/**
 * Authentication Controller - Simplified Email/Password Authentication
 *
 * Implements simple email/password authentication without OTP verification.
 * Uses SQLite/PostgreSQL for user storage and session management.
 */

const crypto = require('crypto');
const { AppError, ValidationError, AuthenticationError, NotFoundError, ConflictError, catchAsync } = require('../utils/errorHandler');
const { generateToken, generateRefreshToken, verifyToken, verifyRefreshToken } = require('../utils/jwt');
const User = require('../models/User');
const Session = require('../models/Session');
const Metrics = require('../models/Metrics');
const logger = require('../utils/logger');

// Configuration for authentication
const AUTH_CONFIG = {
  // Session-based auth
  USE_SESSIONS: process.env.USE_SESSIONS === 'true' || true,
  // Allow simple login (always true now)
  ALLOW_SIMPLE_LOGIN: true,
  // Debug mode
  DEBUG_MODE: process.env.NODE_ENV === 'development'
};

// Debug logging helper
function debugLog(message, data = null) {
  if (AUTH_CONFIG.DEBUG_MODE) {
    logger.info(`[AUTH DEBUG] ${message}`, data ? { data } : {});
    console.log(`[AUTH DEBUG] ${message}`, data || '');
  }
}

// Login with username/password (simplified - no OTP)
exports.login = catchAsync(async (req, res, next) => {
  debugLog('Login attempt started', { username: req.body.username });

  const { username, password } = req.body;

  // Validate input
  if (!username || !password) {
    debugLog('Login failed: Missing username or password');
    return next(new AppError('Username and password are required', 400));
  }

  debugLog('Looking up user in database', { username });

  // Find user in database
  const user = await User.findByUsername(username);
  if (!user) {
    debugLog('Login failed: User not found', { username });
    try {
      await Metrics.recordError(new Error('Login attempt with invalid username'), null, 'auth', 'login_failed');
    } catch (metricsError) {
      debugLog('Metrics recording failed', { error: metricsError.message });
    }
    return next(new AppError('Invalid username or password', 401));
  }

  debugLog('User found, checking account status', { userId: user.id, username: user.username });

  // Check if account is locked
  if (user.isAccountLocked()) {
    debugLog('Login failed: Account is locked', { userId: user.id });
    try {
      await Metrics.recordError(new Error('Login attempt on locked account'), user.id, 'auth', 'account_locked');
    } catch (metricsError) {
      debugLog('Metrics recording failed', { error: metricsError.message });
    }
    return next(new AppError('Account is temporarily locked due to multiple failed login attempts', 423));
  }

  debugLog('Verifying password');

  // Verify password
  const isPasswordValid = await user.verifyPassword(password);
  if (!isPasswordValid) {
    debugLog('Login failed: Invalid password', { userId: user.id });
    try {
      await user.incrementLoginAttempts();
      await Metrics.recordError(new Error('Login attempt with invalid password'), user.id, 'auth', 'login_failed');
    } catch (error) {
      debugLog('Error updating login attempts', { error: error.message });
    }
    return next(new AppError('Invalid username or password', 401));
  }

  debugLog('Password verified, generating tokens');

  // Generate tokens
  const token = generateToken(user.id, user.username);
  const refreshToken = generateRefreshToken(user.id);

  debugLog('Tokens generated, creating session');

  // Create authentication session
  try {
    if (AUTH_CONFIG.USE_SESSIONS) {
      await Session.createAuthSession(user.id, user.username);
      debugLog('Authentication session created');
    }
  } catch (sessionError) {
    debugLog('Session creation failed', { error: sessionError.message });
    // Continue without session - not critical
  }

  // Reset login attempts on successful login
  try {
    await user.resetLoginAttempts();
    debugLog('Login attempts reset');
  } catch (error) {
    debugLog('Failed to reset login attempts', { error: error.message });
    // Continue - not critical
  }

  // Log successful login
  logger.info(`User ${username} logged in successfully`);
  try {
    await Metrics.recordEvent('user_login', user.id, 'auth', { method: 'simple' });
  } catch (metricsError) {
    debugLog('Metrics recording failed', { error: metricsError.message });
  }

  debugLog('Login successful, sending response');

  return res.status(200).json({
    success: true,
    message: 'Login successful',
    token,
    refreshToken,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name
    }
  });
});




// User registration (simplified - no OTP)
exports.signup = catchAsync(async (req, res, next) => {
  debugLog('Signup attempt started', { username: req.body.username, email: req.body.email });

  const { username, email, password, firstName, lastName, fullName } = req.body;

  // Validate input
  if (!username || !email || !password) {
    debugLog('Signup failed: Missing required fields');
    return next(new AppError('Username, email, and password are required', 400));
  }

  debugLog('Checking for existing users');

  // Check if user already exists
  const existingUser = await User.findByUsername(username);
  if (existingUser) {
    debugLog('Signup failed: Username already exists', { username });
    return next(new AppError('Username already exists', 409));
  }

  const existingEmail = await User.findByEmail(email);
  if (existingEmail) {
    debugLog('Signup failed: Email already registered', { email });
    return next(new AppError('Email already registered', 409));
  }

  debugLog('Processing user name fields');

  // Handle both fullName and firstName/lastName formats
  let userFirstName = firstName || '';
  let userLastName = lastName || '';

  if (fullName && !firstName && !lastName) {
    // Split fullName into firstName and lastName
    const nameParts = fullName.trim().split(' ');
    userFirstName = nameParts[0] || '';
    userLastName = nameParts.slice(1).join(' ') || '';
  }

  debugLog('Creating new user');

  // Create new user
  const user = new User({
    username,
    email,
    password,
    firstName: userFirstName,
    lastName: userLastName
  });

  try {
    await user.save();
    debugLog('User saved successfully', { userId: user.id });
  } catch (saveError) {
    debugLog('User save failed', { error: saveError.message });
    throw saveError;
  }

  // Record metrics
  try {
    await Metrics.recordUserAction(user.id, 'signup_success', 'auth');
  } catch (metricsError) {
    debugLog('Metrics recording failed', { error: metricsError.message });
  }

  debugLog('Generating tokens for new user');

  // Generate JWT token for immediate login after registration
  const token = generateToken(user.id, user.username);
  const refreshToken = generateRefreshToken(user.id);

  debugLog('Signup successful, sending response');

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name
    },
    token,
    refreshToken
  });
});

// User logout (simplified)
exports.logout = catchAsync(async (req, res, next) => {
  debugLog('Logout attempt started');

  const { sessionId } = req.body;
  const authHeader = req.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);

    try {
      const decoded = verifyToken(token);
      debugLog('Token decoded for logout', { userId: decoded.sub });

      const sessionIdToInvalidate = sessionId || decoded.sessionId;

      if (sessionIdToInvalidate && AUTH_CONFIG.USE_SESSIONS) {
        try {
          const session = await Session.findBySessionId(sessionIdToInvalidate);
          if (session) {
            await session.invalidate();
            debugLog('Session invalidated', { sessionId: sessionIdToInvalidate });
          }
        } catch (sessionError) {
          debugLog('Session invalidation failed', { error: sessionError.message });
        }
      }

      // Record metrics
      try {
        await Metrics.recordUserAction(decoded.sub, 'logout_success', 'auth');
      } catch (metricsError) {
        debugLog('Metrics recording failed', { error: metricsError.message });
      }
    } catch (error) {
      debugLog('Invalid token during logout', { error: error.message });
    }
  }

  debugLog('Logout completed');

  res.status(200).json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Get current user (simplified)
exports.getCurrentUser = catchAsync(async (req, res, next) => {
  debugLog('Get current user request');

  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    debugLog('No token provided');
    return next(new AppError('No token provided', 401));
  }

  const token = authHeader.substring(7);

  try {
    const decoded = verifyToken(token);
    debugLog('Token decoded', { userId: decoded.sub });

    const user = await User.findById(decoded.sub);

    if (!user) {
      debugLog('User not found', { userId: decoded.sub });
      return next(new AppError('User not found', 404));
    }

    debugLog('User found', { userId: user.id, username: user.username });

    // Verify session is still valid (if sessions are enabled)
    if (decoded.sessionId && AUTH_CONFIG.USE_SESSIONS) {
      try {
        const session = await Session.findBySessionId(decoded.sessionId);
        if (!session || !session.isValid()) {
          debugLog('Session expired', { sessionId: decoded.sessionId });
          return next(new AppError('Session expired', 401));
        }
        debugLog('Session validated', { sessionId: decoded.sessionId });
      } catch (sessionError) {
        debugLog('Session validation failed', { error: sessionError.message });
        // Continue without session validation - not critical
      }
    }

    res.status(200).json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name
      }
    });
  } catch (error) {
    debugLog('Token validation failed', { error: error.message });
    return next(new AppError('Invalid token', 401));
  }
});

// Update password (simplified)
exports.updatePassword = catchAsync(async (req, res, next) => {
  debugLog('Password update attempt');

  const { currentPassword, newPassword } = req.body;
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    debugLog('No token provided for password update');
    return next(new AppError('No token provided', 401));
  }

  if (!currentPassword || !newPassword) {
    debugLog('Missing password fields');
    return next(new AppError('Current password and new password are required', 400));
  }

  const token = authHeader.substring(7);

  try {
    const decoded = verifyToken(token);
    debugLog('Token decoded for password update', { userId: decoded.sub });

    const user = await User.findById(decoded.sub);

    if (!user) {
      debugLog('User not found for password update', { userId: decoded.sub });
      return next(new AppError('User not found', 404));
    }

    debugLog('Verifying current password');

    // Verify current password
    const isCurrentPasswordValid = await user.verifyPassword(currentPassword);
    if (!isCurrentPasswordValid) {
      debugLog('Invalid current password');
      try {
        await Metrics.recordError(new Error('Invalid current password during password update'), user.id, 'auth', 'password_update_failed');
      } catch (metricsError) {
        debugLog('Metrics recording failed', { error: metricsError.message });
      }
      return next(new AppError('Current password is incorrect', 401));
    }

    debugLog('Updating password');

    // Update password
    user.password = newPassword;
    await user.save();

    debugLog('Password updated, invalidating sessions');

    // Invalidate all user sessions for security
    try {
      if (AUTH_CONFIG.USE_SESSIONS) {
        await Session.invalidateAllUserSessions(user.username);
        debugLog('All user sessions invalidated');
      }
    } catch (sessionError) {
      debugLog('Session invalidation failed', { error: sessionError.message });
      // Continue - not critical
    }

    // Record metrics
    try {
      await Metrics.recordUserAction(user.id, 'password_updated', 'auth');
    } catch (metricsError) {
      debugLog('Metrics recording failed', { error: metricsError.message });
    }

    debugLog('Password update successful');

    res.status(200).json({
      success: true,
      message: 'Password updated successfully. Please log in again.'
    });
  } catch (error) {
    debugLog('Token validation failed for password update', { error: error.message });
    return next(new AppError('Invalid token', 401));
  }
});

// Password reset functionality removed - not needed for simplified auth

// Refresh JWT token (simplified)
exports.refreshToken = catchAsync(async (req, res, next) => {
  debugLog('Token refresh attempt');

  const { refreshToken } = req.body;

  if (!refreshToken) {
    debugLog('No refresh token provided');
    return next(new AppError('Refresh token is required', 400));
  }

  try {
    const decoded = verifyRefreshToken(refreshToken);
    debugLog('Refresh token decoded', { userId: decoded.sub });

    const user = await User.findById(decoded.sub);

    if (!user) {
      debugLog('User not found for token refresh', { userId: decoded.sub });
      return next(new AppError('User not found', 404));
    }

    debugLog('Generating new access token');

    // Generate new access token
    const newToken = generateToken(user.id, user.username);

    debugLog('Token refresh successful');

    res.status(200).json({
      success: true,
      token: newToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name
      }
    });
  } catch (error) {
    debugLog('Token refresh failed', { error: error.message });
    return next(new AppError('Invalid refresh token', 401));
  }
});

// MFA functions removed - not needed for simplified auth
exports.enableMfa = catchAsync(async (req, res, next) => {
  return next(new AppError('MFA not available in simplified authentication', 501));
});

exports.disableMfa = catchAsync(async (req, res, next) => {
  return next(new AppError('MFA not available in simplified authentication', 501));
});

exports.getMfaStatus = catchAsync(async (req, res, next) => {
  res.status(200).json({
    success: true,
    mfaEnabled: false,
    message: 'MFA not available in simplified authentication'
  });
});

// Simple login function removed - main login function is now simplified
