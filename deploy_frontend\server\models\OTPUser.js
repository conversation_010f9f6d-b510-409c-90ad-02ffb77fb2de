const database = require('../utils/database');
const logger = require('../utils/logger');

class OTPUser {
    constructor(userData = {}) {
        this.id = userData.id || null;
        this.email = userData.email || '';
        this.username = userData.username || '';
        this.displayName = userData.display_name || userData.displayName || '';
        this.isVerified = userData.is_verified || userData.isVerified || false;
        this.failedAttempts = userData.failed_attempts || userData.failedAttempts || 0;
        this.accountLockedUntil = userData.account_locked_until || userData.accountLockedUntil || null;
        this.lastLogin = userData.last_login || userData.lastLogin || null;
        this.createdAt = userData.created_at || userData.createdAt || null;
        this.updatedAt = userData.updated_at || userData.updatedAt || null;
    }

    /**
     * Generate username from email
     */
    generateUsername(email) {
        const baseUsername = email.split('@')[0].toLowerCase();
        // Remove special characters and ensure it's alphanumeric
        return baseUsername.replace(/[^a-z0-9]/g, '');
    }

    /**
     * Create a new user
     */
    static async create(email, displayName = null) {
        try {
            const username = new OTPUser().generateUsername(email);
            const finalDisplayName = displayName || username;

            const query = `
                INSERT INTO users (email, username, display_name, is_verified, created_at, updated_at)
                VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                RETURNING *
            `;

            const values = [email, username, finalDisplayName, false];
            const result = await database.query(query, values);

            if (result.rows.length === 0) {
                throw new Error('Failed to create user');
            }

            logger.info(`User created successfully: ${email}`);
            return new OTPUser(result.rows[0]);
        } catch (error) {
            if (error.message.includes('UNIQUE constraint failed')) {
                if (error.message.includes('email')) {
                    throw new Error('Email already exists');
                } else if (error.message.includes('username')) {
                    throw new Error('Username already exists');
                }
            }
            logger.error('Error creating user:', error.message);
            throw error;
        }
    }

    /**
     * Find user by email
     */
    static async findByEmail(email) {
        try {
            const query = 'SELECT * FROM users WHERE email = $1';
            const result = await database.query(query, [email]);

            if (result.rows.length === 0) {
                return null;
            }

            return new OTPUser(result.rows[0]);
        } catch (error) {
            logger.error('Error finding user by email:', error.message);
            throw error;
        }
    }

    /**
     * Find user by ID
     */
    static async findById(id) {
        try {
            const query = 'SELECT * FROM users WHERE id = $1';
            const result = await database.query(query, [id]);

            if (result.rows.length === 0) {
                return null;
            }

            return new OTPUser(result.rows[0]);
        } catch (error) {
            logger.error('Error finding user by ID:', error.message);
            throw error;
        }
    }

    /**
     * Save user (create or update)
     */
    async save() {
        try {
            if (this.id) {
                // Update existing user
                const query = `
                    UPDATE users 
                    SET email = $1, display_name = $2, is_verified = $3, 
                        failed_attempts = $4, account_locked_until = $5,
                        last_login = $6, updated_at = CURRENT_TIMESTAMP
                    WHERE id = $7
                    RETURNING *
                `;
                
                const values = [
                    this.email, this.displayName, this.isVerified,
                    this.failedAttempts, this.accountLockedUntil,
                    this.lastLogin, this.id
                ];

                const result = await database.query(query, values);
                return result.rows[0];
            } else {
                // Create new user
                const username = this.generateUsername(this.email);
                const query = `
                    INSERT INTO users (email, username, display_name, is_verified, 
                                     failed_attempts, account_locked_until, last_login,
                                     created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    RETURNING *
                `;
                
                const values = [
                    this.email, username, this.displayName || username, this.isVerified,
                    this.failedAttempts, this.accountLockedUntil, this.lastLogin
                ];

                const result = await database.query(query, values);
                
                if (result.rows.length > 0) {
                    this.id = result.rows[0].id;
                    this.username = result.rows[0].username;
                    this.createdAt = result.rows[0].created_at;
                    this.updatedAt = result.rows[0].updated_at;
                }
                
                return result.rows[0];
            }
        } catch (error) {
            logger.error('Error saving user:', error.message);
            throw error;
        }
    }

    /**
     * Mark user as verified
     */
    async markAsVerified() {
        try {
            this.isVerified = true;
            this.lastLogin = new Date().toISOString();
            await this.save();
            logger.info(`User verified: ${this.email}`);
        } catch (error) {
            logger.error('Error marking user as verified:', error.message);
            throw error;
        }
    }

    /**
     * Update last login
     */
    async updateLastLogin() {
        try {
            this.lastLogin = new Date().toISOString();
            await this.save();
        } catch (error) {
            logger.error('Error updating last login:', error.message);
            throw error;
        }
    }

    /**
     * Check if account is locked
     */
    isAccountLocked() {
        if (!this.accountLockedUntil) {
            return false;
        }
        
        const lockTime = new Date(this.accountLockedUntil);
        return lockTime > new Date();
    }

    /**
     * Increment failed attempts
     */
    async incrementFailedAttempts() {
        try {
            this.failedAttempts += 1;
            
            // Lock account after 5 failed attempts for 30 minutes
            if (this.failedAttempts >= 5) {
                const lockUntil = new Date();
                lockUntil.setMinutes(lockUntil.getMinutes() + 30);
                this.accountLockedUntil = lockUntil.toISOString();
                logger.warn(`Account locked for ${this.email} due to failed attempts`);
            }
            
            await this.save();
        } catch (error) {
            logger.error('Error incrementing failed attempts:', error.message);
            throw error;
        }
    }

    /**
     * Reset failed attempts
     */
    async resetFailedAttempts() {
        try {
            this.failedAttempts = 0;
            this.accountLockedUntil = null;
            await this.save();
        } catch (error) {
            logger.error('Error resetting failed attempts:', error.message);
            throw error;
        }
    }

    /**
     * Get user profile data
     */
    getProfile() {
        return {
            id: this.id,
            email: this.email,
            username: this.username,
            displayName: this.displayName,
            isVerified: this.isVerified,
            lastLogin: this.lastLogin,
            createdAt: this.createdAt
        };
    }

    /**
     * Update profile
     */
    async updateProfile(updates) {
        try {
            if (updates.displayName !== undefined) {
                this.displayName = updates.displayName;
            }
            
            await this.save();
            logger.info(`Profile updated for user: ${this.email}`);
            return this.getProfile();
        } catch (error) {
            logger.error('Error updating profile:', error.message);
            throw error;
        }
    }

    /**
     * Delete user
     */
    async delete() {
        try {
            if (!this.id) {
                throw new Error('Cannot delete user without ID');
            }

            const query = 'DELETE FROM users WHERE id = $1';
            await database.query(query, [this.id]);
            
            logger.info(`User deleted: ${this.email}`);
        } catch (error) {
            logger.error('Error deleting user:', error.message);
            throw error;
        }
    }

    /**
     * Get all users (admin function)
     */
    static async getAll(limit = 50, offset = 0) {
        try {
            const query = `
                SELECT id, email, username, display_name, is_verified, 
                       last_login, created_at, updated_at
                FROM users 
                ORDER BY created_at DESC 
                LIMIT $1 OFFSET $2
            `;
            
            const result = await database.query(query, [limit, offset]);
            return result.rows.map(row => new OTPUser(row));
        } catch (error) {
            logger.error('Error getting all users:', error.message);
            throw error;
        }
    }

    /**
     * Get user count
     */
    static async getCount() {
        try {
            const query = 'SELECT COUNT(*) as count FROM users';
            const result = await database.query(query);
            return parseInt(result.rows[0].count);
        } catch (error) {
            logger.error('Error getting user count:', error.message);
            throw error;
        }
    }
}

module.exports = OTPUser;
