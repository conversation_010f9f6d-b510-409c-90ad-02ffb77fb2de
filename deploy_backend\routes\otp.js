const express = require('express');
const otpController = require('../controllers/otpController');
const { protect, optionalAuth, requireAdmin } = require('../middleware/otpAuth');

const router = express.Router();

/**
 * @route   POST /api/otp/signup/request
 * @desc    Request OTP for signup
 * @access  Public
 */
router.post('/signup/request', otpController.requestSignupOTP);

/**
 * @route   POST /api/otp/signin/request
 * @desc    Request OTP for signin
 * @access  Public
 */
router.post('/signin/request', otpController.requestSigninOTP);

/**
 * @route   POST /api/otp/verify
 * @desc    Verify OTP and complete authentication
 * @access  Public
 */
router.post('/verify', otpController.verifyOTP);

/**
 * @route   GET /api/otp/status
 * @desc    Check authentication status
 * @access  Protected
 */
router.get('/status', protect, otpController.getStatus);

/**
 * @route   GET /api/otp/me
 * @desc    Get current user profile
 * @access  Protected
 */
router.get('/me', protect, otpController.getProfile);

/**
 * @route   PUT /api/otp/profile
 * @desc    Update user profile
 * @access  Protected
 */
router.put('/profile', protect, otpController.updateProfile);

/**
 * @route   POST /api/otp/logout
 * @desc    Logout user
 * @access  Protected
 */
router.post('/logout', protect, otpController.logout);

/**
 * @route   GET /api/otp/stats
 * @desc    Get OTP service statistics
 * @access  Protected (Admin)
 */
router.get('/stats', protect, requireAdmin, otpController.getOTPStats);

module.exports = router;
