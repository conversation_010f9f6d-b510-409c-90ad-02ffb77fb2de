<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1200" viewBox="0 0 1200 1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#0d2b45" stroke-width="0.5" opacity="0.3"/>
    </pattern>
    <pattern id="smallGrid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#0d2b45" stroke-width="0.2" opacity="0.2"/>
    </pattern>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0d2b45" stop-opacity="0.05"/>
      <stop offset="100%" stop-color="#4a90e2" stop-opacity="0.1"/>
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#blueGradient)"/>
  
  <!-- Grid patterns -->
  <rect width="100%" height="100%" fill="url(#smallGrid)"/>
  <rect width="100%" height="100%" fill="url(#grid)"/>
  
  <!-- Digital circuit elements -->
  <g opacity="0.2" stroke="#4a90e2" stroke-width="1.5" fill="none">
    <!-- Horizontal lines -->
    <path d="M 100,200 H 1100" />
    <path d="M 200,400 H 1000" />
    <path d="M 300,600 H 900" />
    <path d="M 150,800 H 1050" />
    <path d="M 250,1000 H 950" />
    
    <!-- Vertical lines -->
    <path d="M 200,100 V 1100" />
    <path d="M 400,200 V 1000" />
    <path d="M 600,150 V 1050" />
    <path d="M 800,250 V 950" />
    <path d="M 1000,300 V 900" />
    
    <!-- Connection nodes -->
    <circle cx="200" cy="200" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="400" cy="400" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="600" cy="600" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="800" cy="800" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="1000" cy="1000" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="200" cy="800" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="400" cy="600" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="600" cy="400" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="800" cy="200" r="5" fill="#4a90e2" filter="url(#glow)"/>
    <circle cx="1000" cy="600" r="5" fill="#4a90e2" filter="url(#glow)"/>
    
    <!-- Circuit elements -->
    <rect x="350" y="350" width="50" height="50" rx="5" ry="5" />
    <rect x="750" y="750" width="50" height="50" rx="5" ry="5" />
    <rect x="550" y="550" width="100" height="30" rx="5" ry="5" />
    <rect x="250" y="750" width="30" height="100" rx="5" ry="5" />
    <rect x="850" y="250" width="30" height="100" rx="5" ry="5" />
    
    <!-- Binary data -->
    <text x="380" y="380" fill="#4a90e2" font-family="monospace" font-size="12">01</text>
    <text x="780" y="780" fill="#4a90e2" font-family="monospace" font-size="12">10</text>
    <text x="580" y="570" fill="#4a90e2" font-family="monospace" font-size="12">0110</text>
    <text x="260" y="800" fill="#4a90e2" font-family="monospace" font-size="12">11</text>
    <text x="860" y="300" fill="#4a90e2" font-family="monospace" font-size="12">01</text>
  </g>
</svg>
