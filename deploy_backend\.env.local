# LESAVOT API Server Local Development Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# SQLite Configuration for Local Development
USE_SQLITE=true
SQLITE_DB_PATH=./database/lesavot.db

# JWT Configuration
JWT_SECRET=LeSaVoT_2024_SuperSecure_JWT_Key_Development_9x8y7z6w5v4u3t2s1r0q
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (for OTP) - Disabled for local testing
EMAIL_SERVICE=gmail
EMAIL_USER=
EMAIL_PASSWORD=
EMAIL_FROM=LESAVOT Security <<EMAIL>>

# Alternative SMTP Configuration (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_SECURE=false

# Security Configuration
ENABLE_HELMET=true
ENABLE_CONTENT_SECURITY_POLICY=false
ENABLE_XSS_PROTECTION=true
SHOW_STACK_TRACES=true
LOG_ERRORS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://127.0.0.1:8080,http://localhost:8082,http://127.0.0.1:8082
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Logging Configuration
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,audio/wav,audio/mp3,text/plain

# Session Configuration
SESSION_SECRET=LeSaVoT_Session_Secret_2024_Development_a1b2c3d4e5f6g7h8i9j0
SESSION_TIMEOUT=1800000

# OTP Configuration
OTP_EXPIRY_SECONDS=300
OTP_LENGTH=6

# Authentication Configuration
REQUIRE_OTP=false
ALLOW_SIMPLE_LOGIN=true
USE_SESSIONS=true

# Frontend URL
FRONTEND_URL=http://localhost:8082
