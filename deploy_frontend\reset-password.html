<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - LESAVOT</title>
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <!-- Header -->
            <div class="auth-header">
                <div class="logo-container">
                    <i class="fas fa-shield-alt logo-icon"></i>
                    <h1 class="logo-text">LESAVOT</h1>
                </div>
                <p class="auth-subtitle">Secure Password Reset</p>
            </div>

            <!-- Notification Area -->
            <div id="notificationArea" class="notification-area"></div>

            <!-- Reset Password Form -->
            <div class="auth-form-container">
                <div class="form-header">
                    <i class="fas fa-key"></i>
                    <h2>Reset Your Password</h2>
                </div>

                <form id="resetPasswordForm" class="auth-form">
                    <div class="form-group">
                        <label for="newPassword">New Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="newPassword" name="newPassword" 
                                   autocomplete="new-password" required>
                            <button type="button" class="password-toggle" id="toggleNewPassword" 
                                    aria-label="Show password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="helper-text">
                            Choose a strong password with at least 8 characters
                        </div>
                    </div>

                    <!-- Password Strength Meter -->
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-segment" id="strength1"></div>
                            <div class="strength-segment" id="strength2"></div>
                            <div class="strength-segment" id="strength3"></div>
                            <div class="strength-segment" id="strength4"></div>
                        </div>
                        <div class="strength-text" id="strengthText">Password strength</div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm New Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" 
                                   autocomplete="new-password" required>
                            <button type="button" class="password-toggle" id="toggleConfirmPassword" 
                                    aria-label="Show password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="helper-text">
                            Re-enter your new password to confirm
                        </div>
                    </div>

                    <button type="submit" class="auth-btn" id="resetPasswordBtn">
                        <i class="fas fa-shield-alt"></i>
                        Reset Password
                    </button>
                </form>

                <div class="auth-links">
                    <a href="auth.html" class="auth-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Sign In
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="reset-password.js"></script>
</body>
</html>
