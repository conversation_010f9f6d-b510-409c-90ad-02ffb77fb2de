/**
 * OTP Authentication System for LESAVOT
 * Handles email + OTP verification for signup and signin
 */

class OTPAuth {
    constructor() {
        this.apiBaseUrl = (window.CONFIG && window.CONFIG.apiBaseUrl) || 'http://localhost:3000/api';
        this.currentUser = null;
        this.isInitialized = false;
        this.init();
    }

    /**
     * Initialize the authentication system
     */
    async init() {
        try {
            // Check if user is already authenticated
            const token = this.getStoredToken();
            if (token) {
                await this.checkAuthStatus();
            }
            
            this.setupEventListeners();
            this.isInitialized = true;
            
            console.log('OTP Authentication system initialized');
        } catch (error) {
            console.error('Failed to initialize OTP auth:', error);
        }
    }

    /**
     * Setup event listeners for forms
     */
    setupEventListeners() {
        // Signup form
        const signupForm = document.getElementById('signupForm');
        const signupBtn = document.getElementById('signupBtn');
        
        if (signupBtn) {
            signupBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignUp();
            });
        }

        // Signin form
        const signinForm = document.getElementById('signinForm');
        const signinBtn = document.getElementById('signinBtn');
        
        if (signinBtn) {
            signinBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignIn();
            });
        }

        // OTP verification form
        const verifyBtn = document.getElementById('verifyBtn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleOTPVerification();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }

        // Resend OTP button
        const resendBtn = document.getElementById('resendOTPBtn');
        if (resendBtn) {
            resendBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.resendOTP();
            });
        }
    }

    /**
     * Handle signup process
     */
    async handleSignUp() {
        try {
            const emailInput = document.getElementById('signupEmail');
            const email = emailInput?.value?.trim();

            if (!email) {
                this.showMessage('Please enter your email address', 'error');
                return;
            }

            if (!this.isValidEmail(email)) {
                this.showMessage('Please enter a valid email address', 'error');
                return;
            }

            this.showLoading('Sending verification code...');

            const response = await fetch(`${this.apiBaseUrl}/auth/signup/request`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok) {
                this.showMessage('Verification code sent to your email!', 'success');
                this.showOTPForm(email, 'signup');
                this.startOTPTimer(data.expiresAt);
            } else {
                this.showMessage(data.message || 'Failed to send verification code', 'error');
            }

        } catch (error) {
            console.error('Sign up error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle signin process
     */
    async handleSignIn() {
        try {
            const emailInput = document.getElementById('signinEmail');
            const email = emailInput?.value?.trim();

            if (!email) {
                this.showMessage('Please enter your email address', 'error');
                return;
            }

            if (!this.isValidEmail(email)) {
                this.showMessage('Please enter a valid email address', 'error');
                return;
            }

            this.showLoading('Sending verification code...');

            const response = await fetch(`${this.apiBaseUrl}/auth/signin/request`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok) {
                this.showMessage('Verification code sent to your email!', 'success');
                this.showOTPForm(email, 'signin');
                this.startOTPTimer(data.expiresAt);
            } else {
                this.showMessage(data.message || 'Failed to send verification code', 'error');
            }

        } catch (error) {
            console.error('Sign in error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle OTP verification
     */
    async handleOTPVerification() {
        try {
            const otpInput = document.getElementById('otpCode');
            const emailInput = document.getElementById('verifyEmail');
            
            const otp = otpInput?.value?.trim();
            const email = emailInput?.value?.trim();

            if (!otp || !email) {
                this.showMessage('Please enter the verification code', 'error');
                return;
            }

            if (otp.length !== 6 || !/^\d{6}$/.test(otp)) {
                this.showMessage('Please enter a valid 6-digit code', 'error');
                return;
            }

            this.showLoading('Verifying code...');

            const response = await fetch(`${this.apiBaseUrl}/auth/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, otp })
            });

            const data = await response.json();

            if (response.ok) {
                // Store the session token
                this.storeToken(data.sessionToken);
                this.currentUser = data.user;
                
                this.showMessage(`${data.type === 'signup' ? 'Account created' : 'Sign in'} successful!`, 'success');
                
                // Redirect to main application
                setTimeout(() => {
                    window.location.href = '/index.html';
                }, 1500);
                
            } else {
                this.showMessage(data.message || 'Verification failed', 'error');
            }

        } catch (error) {
            console.error('OTP verification error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Show OTP verification form
     */
    showOTPForm(email, type) {
        // Hide auth forms
        const authForms = document.querySelectorAll('.auth-form');
        authForms.forEach(form => form.style.display = 'none');

        // Show OTP form
        let otpForm = document.getElementById('otpForm');
        if (!otpForm) {
            otpForm = this.createOTPForm();
            document.querySelector('.auth-container')?.appendChild(otpForm);
        }

        // Set email in hidden input
        const emailInput = document.getElementById('verifyEmail');
        if (emailInput) {
            emailInput.value = email;
        }

        // Update form title
        const title = document.getElementById('otpTitle');
        if (title) {
            title.textContent = type === 'signup' ? 'Complete Your Signup' : 'Complete Your Sign In';
        }

        // Show the form
        otpForm.style.display = 'block';

        // Focus on OTP input
        const otpInput = document.getElementById('otpCode');
        if (otpInput) {
            otpInput.focus();
        }
    }

    /**
     * Create OTP verification form
     */
    createOTPForm() {
        const formHTML = `
            <div id="otpForm" class="auth-form" style="display: none;">
                <h2 id="otpTitle">Enter Verification Code</h2>
                <p>We've sent a 6-digit verification code to your email address.</p>
                
                <div class="form-group">
                    <input type="hidden" id="verifyEmail" />
                    <input type="text" id="otpCode" placeholder="Enter 6-digit code" maxlength="6" pattern="[0-9]{6}" />
                </div>
                
                <button id="verifyBtn" class="auth-btn">Verify Code</button>
                
                <div class="otp-timer">
                    <p>Code expires in: <span id="otpTimer">10:00</span></p>
                </div>
                
                <div class="resend-section">
                    <p>Didn't receive the code?</p>
                    <button id="resendOTPBtn" class="link-btn" disabled>Resend Code</button>
                </div>
                
                <div class="back-section">
                    <button id="backToAuthBtn" class="link-btn">← Back to Sign In</button>
                </div>
            </div>
        `;
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formHTML;
        return tempDiv.firstElementChild;
    }

    /**
     * Start OTP expiration timer
     */
    startOTPTimer(expiresAt) {
        const timerElement = document.getElementById('otpTimer');
        const resendBtn = document.getElementById('resendOTPBtn');
        
        if (!timerElement) return;

        const updateTimer = () => {
            const now = Date.now();
            const timeLeft = expiresAt - now;
            
            if (timeLeft <= 0) {
                timerElement.textContent = '00:00';
                if (resendBtn) resendBtn.disabled = false;
                return;
            }
            
            const minutes = Math.floor(timeLeft / 60000);
            const seconds = Math.floor((timeLeft % 60000) / 1000);
            timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        };

        updateTimer();
        const interval = setInterval(updateTimer, 1000);
        
        // Clear interval when timer expires
        setTimeout(() => {
            clearInterval(interval);
            if (resendBtn) resendBtn.disabled = false;
        }, expiresAt - Date.now());
    }

    /**
     * Resend OTP
     */
    async resendOTP() {
        const emailInput = document.getElementById('verifyEmail');
        const email = emailInput?.value;
        
        if (!email) {
            this.showMessage('Email not found. Please start over.', 'error');
            return;
        }

        // Determine if this was signup or signin (you might want to store this)
        // For now, we'll default to signin
        await this.handleSignIn();
    }

    /**
     * Utility functions
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showMessage(message, type = 'info') {
        // Implementation depends on your UI framework
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // You can implement toast notifications or update a message div
        const messageDiv = document.getElementById('authMessage');
        if (messageDiv) {
            messageDiv.textContent = message;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
        }
    }

    showLoading(message = 'Loading...') {
        const loadingDiv = document.getElementById('loadingMessage');
        if (loadingDiv) {
            loadingDiv.textContent = message;
            loadingDiv.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingDiv = document.getElementById('loadingMessage');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    storeToken(token) {
        localStorage.setItem('lesavot_session_token', token);
    }

    getStoredToken() {
        return localStorage.getItem('lesavot_session_token');
    }

    removeStoredToken() {
        localStorage.removeItem('lesavot_session_token');
    }

    async checkAuthStatus() {
        try {
            const token = this.getStoredToken();
            if (!token) return false;

            const response = await fetch(`${this.apiBaseUrl}/auth/status`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                return true;
            } else {
                this.removeStoredToken();
                return false;
            }
        } catch (error) {
            console.error('Auth status check failed:', error);
            return false;
        }
    }

    async logout() {
        try {
            const token = this.getStoredToken();
            if (token) {
                await fetch(`${this.apiBaseUrl}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.removeStoredToken();
            this.currentUser = null;
            window.location.href = '/auth.html';
        }
    }

    isAuthenticated() {
        return !!this.currentUser && !!this.getStoredToken();
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.otpAuth = new OTPAuth();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OTPAuth;
}
