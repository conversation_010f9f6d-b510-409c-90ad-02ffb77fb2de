/**
 * JWT Utility Functions for LESAVOT Authentication
 *
 * This module provides functions for generating and verifying JWT tokens
 * for email OTP authentication during signin.
 */

const jwt = require('jsonwebtoken');
const logger = require('./logger');

/**
 * Generate a JWT token for a user
 * @param {String} userId - The user's ID
 * @param {String} expiresIn - Token expiration time (e.g., '7d', '24h')
 * @param {Object} additionalClaims - Additional claims to include in the token
 * @returns {String} - JWT token
 */
exports.generateToken = (userId, expiresIn = null, additionalClaims = {}) => {
  try {
    const payload = {
      sub: userId,
      iat: Math.floor(Date.now() / 1000),
      ...additionalClaims
    };

    const token = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      {
        expiresIn: expiresIn || process.env.JWT_EXPIRES_IN || '24h',
        algorithm: 'HS256'
      }
    );

    logger.info('JWT token generated for user:', userId);
    return token;
  } catch (error) {
    logger.error('Error generating JWT token:', error);
    throw new Error('Failed to generate authentication token');
  }
};

/**
 * Generate a refresh token for a user
 * @param {String} userId - The user's ID
 * @returns {String} - JWT refresh token
 */
exports.generateRefreshToken = (userId) => {
  try {
    return jwt.sign(
      { sub: userId },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        algorithm: 'HS256'
      }
    );
  } catch (error) {
    logger.error('Error generating refresh token:', error);
    throw new Error('Failed to generate refresh token');
  }
};

/**
 * Verify a JWT token
 * @param {String} token - JWT token to verify
 * @returns {Object} - Decoded token payload
 */
exports.verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET, { algorithms: ['HS256'] });
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      logger.warn('Token expired:', { error: error.message });
      throw new Error('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid token:', { error: error.message });
      throw new Error('Invalid token');
    } else {
      logger.error('Error verifying token:', error);
      throw new Error('Token verification failed');
    }
  }
};

/**
 * Verify a refresh token
 * @param {String} token - Refresh token to verify
 * @returns {Object} - Decoded token payload
 */
exports.verifyRefreshToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET, { algorithms: ['HS256'] });
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      logger.warn('Refresh token expired:', { error: error.message });
      throw new Error('Refresh token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid refresh token:', { error: error.message });
      throw new Error('Invalid refresh token');
    } else {
      logger.error('Error verifying refresh token:', error);
      throw new Error('Refresh token verification failed');
    }
  }
};

/**
 * Generate a JWT token for OTP email verification
 * @param {String} email - User's email address
 * @param {String} otp - OTP code
 * @param {String} expiresIn - Token expiration time (e.g., '5m')
 * @returns {String} - JWT token for OTP verification
 */
exports.generateOtpToken = (email, otp, expiresIn = '5m') => {
  try {
    const payload = {
      email,
      otp,
      type: 'email_otp',
      iat: Math.floor(Date.now() / 1000)
    };

    const token = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      {
        expiresIn,
        algorithm: 'HS256'
      }
    );

    logger.info('OTP JWT token generated for email:', email);
    return token;
  } catch (error) {
    logger.error('Error generating OTP JWT token:', error);
    throw new Error('Failed to generate OTP token');
  }
};

/**
 * Verify an OTP JWT token
 * @param {String} token - JWT token containing OTP
 * @returns {Object} - Decoded token payload with email and OTP
 */
exports.verifyOtpToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { algorithms: ['HS256'] });

    if (decoded.type !== 'email_otp') {
      throw new Error('Invalid token type for OTP verification');
    }

    logger.info('OTP JWT token verified for email:', decoded.email);
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      logger.warn('OTP token expired:', { error: error.message });
      throw new Error('OTP token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid OTP token:', { error: error.message });
      throw new Error('Invalid OTP token');
    } else {
      logger.error('Error verifying OTP token:', error);
      throw new Error('OTP token verification failed');
    }
  }
};
