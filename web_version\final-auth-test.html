<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Authentication Test - LESAVOT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .test-panel {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.error {
            background: #dc3545;
        }
        .result-area {
            background: #333;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #555;
            background: #333;
            color: #fff;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        h1, h2, h3 {
            color: #00d4ff;
        }
        .summary {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00d4ff;
        }
    </style>
</head>
<body>
    <h1>🧪 Final Authentication Test Suite - LESAVOT</h1>
    
    <div class="summary">
        <h2>🎯 Test Objectives</h2>
        <ul>
            <li>✅ Verify backend server connectivity</li>
            <li>✅ Test API endpoint availability</li>
            <li>✅ Validate signup functionality</li>
            <li>✅ Validate signin functionality</li>
            <li>✅ Test complete authentication flow</li>
            <li>✅ Verify database operations</li>
        </ul>
    </div>

    <div class="test-grid">
        <!-- System Status Panel -->
        <div class="test-panel">
            <h3>🔍 System Status</h3>
            <div id="systemStatus">
                <p><span id="backendStatus" class="status-indicator status-offline"></span>Backend Server</p>
                <p><span id="frontendStatus" class="status-indicator status-offline"></span>Frontend Server</p>
                <p><span id="apiStatus" class="status-indicator status-offline"></span>API Endpoints</p>
            </div>
            <button class="test-button" onclick="checkSystemStatus()">Check System Status</button>
            <div id="statusResults" class="result-area"></div>
        </div>

        <!-- API Tests Panel -->
        <div class="test-panel">
            <h3>🌐 API Tests</h3>
            <button class="test-button" onclick="testHealthEndpoint()">Test Health</button>
            <button class="test-button" onclick="testSignupEndpoint()">Test Signup API</button>
            <button class="test-button" onclick="testSigninEndpoint()">Test Signin API</button>
            <button class="test-button" onclick="runAllAPITests()">Run All API Tests</button>
            <div id="apiResults" class="result-area"></div>
        </div>

        <!-- Authentication Flow Panel -->
        <div class="test-panel">
            <h3>🔐 Authentication Flow</h3>
            <div class="form-group">
                <label>Test Username:</label>
                <input type="text" id="testUsername" placeholder="Enter test username">
            </div>
            <div class="form-group">
                <label>Test Email:</label>
                <input type="email" id="testEmail" placeholder="Enter test email">
            </div>
            <div class="form-group">
                <label>Test Password:</label>
                <input type="password" id="testPassword" placeholder="Enter test password">
            </div>
            <button class="test-button" onclick="generateTestUser()">Generate Test User</button>
            <button class="test-button" onclick="testCompleteFlow()">Test Complete Flow</button>
            <div id="flowResults" class="result-area"></div>
        </div>

        <!-- Database Tests Panel -->
        <div class="test-panel">
            <h3>🗄️ Database Tests</h3>
            <button class="test-button" onclick="testUserCreation()">Test User Creation</button>
            <button class="test-button" onclick="testUserRetrieval()">Test User Retrieval</button>
            <button class="test-button" onclick="testSessionManagement()">Test Sessions</button>
            <button class="test-button" onclick="viewDatabaseStats()">View DB Stats</button>
            <div id="databaseResults" class="result-area"></div>
        </div>
    </div>

    <!-- Comprehensive Test Results -->
    <div class="test-panel">
        <h3>📊 Comprehensive Test Results</h3>
        <button class="test-button" onclick="runComprehensiveTest()">🚀 Run Complete Test Suite</button>
        <button class="test-button" onclick="clearAllResults()">🧹 Clear All Results</button>
        <div id="comprehensiveResults" class="result-area"></div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:3000/api/v1';
        const HEALTH_URL = 'http://localhost:3000/api/health';
        const FRONTEND_URL = 'http://localhost:8081';

        // Utility functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            console.log(logMessage);
            return logMessage;
        }

        function updateResults(elementId, content, append = false) {
            const element = document.getElementById(elementId);
            if (append) {
                element.textContent += content;
            } else {
                element.textContent = content;
            }
            element.scrollTop = element.scrollHeight;
        }

        function updateStatus(elementId, online) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator ${online ? 'status-online' : 'status-offline'}`;
        }

        // System Status Tests
        async function checkSystemStatus() {
            let output = log('🔍 Checking system status...', 'info');
            updateResults('statusResults', output);

            try {
                // Test backend
                const healthResponse = await fetch(HEALTH_URL);
                if (healthResponse.ok) {
                    updateStatus('backendStatus', true);
                    output += log('Backend server: ONLINE', 'success');
                } else {
                    updateStatus('backendStatus', false);
                    output += log('Backend server: ERROR', 'error');
                }

                // Test frontend
                updateStatus('frontendStatus', true);
                output += log(`Frontend server: ONLINE (${FRONTEND_URL})`, 'success');

                // Test API endpoints
                const apiTests = [
                    `${API_BASE}/auth/signup`,
                    `${API_BASE}/auth/signin`
                ];

                let apiOnline = true;
                for (const endpoint of apiTests) {
                    try {
                        const response = await fetch(endpoint, { method: 'OPTIONS' });
                        if (!response.ok) apiOnline = false;
                    } catch (error) {
                        apiOnline = false;
                    }
                }

                updateStatus('apiStatus', apiOnline);
                output += log(`API endpoints: ${apiOnline ? 'ONLINE' : 'ERROR'}`, apiOnline ? 'success' : 'error');

                updateResults('statusResults', output);

            } catch (error) {
                output += log(`System check error: ${error.message}`, 'error');
                updateResults('statusResults', output);
            }
        }

        // API Tests
        async function testHealthEndpoint() {
            let output = log('🏥 Testing health endpoint...', 'info');
            updateResults('apiResults', output);

            try {
                const response = await fetch(HEALTH_URL);
                const data = await response.json();

                if (response.ok) {
                    output += log('Health endpoint: SUCCESS', 'success');
                    output += log(`Status: ${data.status}`, 'info');
                    output += log(`Users: ${data.users}, Sessions: ${data.sessions}`, 'info');
                } else {
                    output += log('Health endpoint: FAILED', 'error');
                }

                updateResults('apiResults', output);

            } catch (error) {
                output += log(`Health test error: ${error.message}`, 'error');
                updateResults('apiResults', output);
            }
        }

        async function testSignupEndpoint() {
            let output = log('📝 Testing signup endpoint...', 'info');
            updateResults('apiResults', output);

            const testUser = {
                username: 'apitest_' + Date.now(),
                email: 'apitest_' + Date.now() + '@example.com',
                password: 'TestPassword123!',
                confirmPassword: 'TestPassword123!'
            };

            try {
                const response = await fetch(`${API_BASE}/auth/signup`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testUser)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    output += log('Signup endpoint: SUCCESS', 'success');
                    output += log(`User created: ${data.user.username}`, 'success');
                } else {
                    output += log(`Signup endpoint: FAILED - ${data.message}`, 'error');
                }

                updateResults('apiResults', output);

            } catch (error) {
                output += log(`Signup test error: ${error.message}`, 'error');
                updateResults('apiResults', output);
            }
        }

        async function testSigninEndpoint() {
            let output = log('🔐 Testing signin endpoint...', 'info');
            updateResults('apiResults', output);

            // Use existing user
            const testUser = {
                username: 'TCHANGA BECHI Jacques',
                password: 'TestPassword123!'
            };

            try {
                const response = await fetch(`${API_BASE}/auth/signin`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testUser)
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    output += log('Signin endpoint: SUCCESS', 'success');
                    output += log(`User signed in: ${data.user.username}`, 'success');
                    output += log(`Token: ${data.sessionToken.substring(0, 20)}...`, 'info');
                } else {
                    output += log(`Signin endpoint: FAILED - ${data.message}`, 'error');
                }

                updateResults('apiResults', output);

            } catch (error) {
                output += log(`Signin test error: ${error.message}`, 'error');
                updateResults('apiResults', output);
            }
        }

        // Authentication Flow Tests
        function generateTestUser() {
            const timestamp = Date.now();
            document.getElementById('testUsername').value = `flowtest_${timestamp}`;
            document.getElementById('testEmail').value = `flowtest_${timestamp}@example.com`;
            document.getElementById('testPassword').value = 'TestPassword123!';
            log('Generated test user credentials', 'info');
        }

        async function testCompleteFlow() {
            const username = document.getElementById('testUsername').value;
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;

            if (!username || !email || !password) {
                updateResults('flowResults', log('Please fill in all test user fields', 'error'));
                return;
            }

            let output = log('🔄 Testing complete authentication flow...', 'info');
            updateResults('flowResults', output);

            try {
                // Step 1: Signup
                output += log('Step 1: Creating user account...', 'info');
                const signupResponse = await fetch(`${API_BASE}/auth/signup`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username, email, password, confirmPassword: password
                    })
                });

                const signupData = await signupResponse.json();

                if (signupResponse.ok && signupData.success) {
                    output += log('✅ Signup successful', 'success');
                } else {
                    output += log(`❌ Signup failed: ${signupData.message}`, 'error');
                    updateResults('flowResults', output);
                    return;
                }

                // Step 2: Signin
                output += log('Step 2: Signing in with new account...', 'info');
                const signinResponse = await fetch(`${API_BASE}/auth/signin`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                const signinData = await signinResponse.json();

                if (signinResponse.ok && signinData.success) {
                    output += log('✅ Signin successful', 'success');
                    output += log(`Session token: ${signinData.sessionToken.substring(0, 20)}...`, 'info');
                    output += log('🎉 Complete authentication flow: SUCCESS', 'success');
                } else {
                    output += log(`❌ Signin failed: ${signinData.message}`, 'error');
                }

                updateResults('flowResults', output);

            } catch (error) {
                output += log(`Flow test error: ${error.message}`, 'error');
                updateResults('flowResults', output);
            }
        }

        // Database Tests
        async function viewDatabaseStats() {
            let output = log('📊 Viewing database statistics...', 'info');
            updateResults('databaseResults', output);

            try {
                const response = await fetch(HEALTH_URL);
                const data = await response.json();

                if (response.ok) {
                    output += log(`Total users: ${data.users}`, 'info');
                    output += log(`Active sessions: ${data.sessions}`, 'info');
                    output += log(`Server uptime: ${data.uptime || 'N/A'}`, 'info');
                    output += log('Database stats retrieved successfully', 'success');
                } else {
                    output += log('Failed to retrieve database stats', 'error');
                }

                updateResults('databaseResults', output);

            } catch (error) {
                output += log(`Database stats error: ${error.message}`, 'error');
                updateResults('databaseResults', output);
            }
        }

        // Comprehensive Test
        async function runComprehensiveTest() {
            let output = log('🚀 Running comprehensive test suite...', 'info');
            updateResults('comprehensiveResults', output);

            // Generate test user
            generateTestUser();
            
            // Run all tests
            await checkSystemStatus();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testHealthEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSignupEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSigninEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCompleteFlow();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await viewDatabaseStats();

            output += log('🎉 Comprehensive test suite completed!', 'success');
            updateResults('comprehensiveResults', output, true);
        }

        // Utility functions
        async function runAllAPITests() {
            await testHealthEndpoint();
            await testSignupEndpoint();
            await testSigninEndpoint();
        }

        function clearAllResults() {
            const resultAreas = ['statusResults', 'apiResults', 'flowResults', 'databaseResults', 'comprehensiveResults'];
            resultAreas.forEach(id => {
                document.getElementById(id).textContent = '';
            });
        }

        // Auto-run system check on load
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
