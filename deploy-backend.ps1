# Deploy Backend to GitHub Repository
# Repository: https://github.com/Bechi-cyber/LASAVOT-Backend

Write-Host "🚀 LESAVOT Backend Deployment Script" -ForegroundColor Yellow
Write-Host "Repository: https://github.com/Bechi-cyber/LASAVOT-Backend" -ForegroundColor Cyan
Write-Host "Source: web_version/server directory" -ForegroundColor Cyan

# Set variables
$backendRepo = "https://github.com/Bechi-cyber/LASAVOT-Backend.git"
$sourceDir = "web_version\server"
$tempDir = "temp_backend_deploy"

# Function to display colored messages
function Write-ColoredOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    Write-Host $Message -ForegroundColor $ForegroundColor
}

try {
    # Check if Git is available
    Write-ColoredOutput "🔍 Checking Git installation..." "Cyan"
    $gitVersion = git --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-ColoredOutput "❌ Git is not installed or not in PATH" "Red"
        exit 1
    }
    Write-ColoredOutput "✅ Git found: $gitVersion" "Green"

    # Check if source directory exists
    if (-not (Test-Path $sourceDir)) {
        Write-ColoredOutput "❌ Source directory '$sourceDir' not found" "Red"
        exit 1
    }
    Write-ColoredOutput "✅ Source directory found: $sourceDir" "Green"

    # Clean up any existing temp directory
    if (Test-Path $tempDir) {
        Write-ColoredOutput "🧹 Cleaning up existing temp directory..." "Yellow"
        Remove-Item -Recurse -Force $tempDir
    }

    # Create temp directory and navigate to it
    Write-ColoredOutput "📁 Creating temporary deployment directory..." "Cyan"
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    Set-Location $tempDir

    # Clone the repository
    Write-ColoredOutput "📥 Cloning backend repository..." "Cyan"
    git clone $backendRepo . 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-ColoredOutput "❌ Failed to clone repository. Initializing new repository..." "Yellow"
        git init
        git remote add origin $backendRepo
    }

    # Copy backend files
    Write-ColoredOutput "📋 Copying backend files..." "Cyan"
    $sourceFullPath = Join-Path ".." $sourceDir
    Copy-Item -Path "$sourceFullPath\*" -Destination "." -Recurse -Force

    # Create/update README for backend
    Write-ColoredOutput "📝 Creating README.md..." "Cyan"
    $readmeContent = @"
# LESAVOT Backend API - Advanced Steganography Platform

## 🔐 Secure Backend API for Multimodal Steganography

This is the backend API server for the LESAVOT steganography platform, providing secure authentication, user management, and steganography operation tracking.

## ✨ Features

### 🔒 Authentication System
- JWT-based authentication
- Secure user registration and login
- Session management
- Password validation and security
- CORS protection

### 🛡️ API Endpoints
- **Authentication**: `/api/v1/auth/*`
  - `POST /auth/signup` - User registration
  - `POST /auth/signin` - User authentication
  - `GET /auth/me` - Get user info
  - `POST /auth/logout` - User logout
- **Health Check**: `/api/health` - Server status and metrics

### 🔧 Technical Features
- Express.js framework
- SQLite database for development
- PostgreSQL support for production
- Environment-based configuration
- Comprehensive error handling
- Security middleware (Helmet, CORS)
- Request logging and monitoring

## 🚀 Live API

- **Production**: [https://lasavot-backend.onrender.com](https://lasavot-backend.onrender.com)
- **Health Check**: [https://lasavot-backend.onrender.com/api/health](https://lasavot-backend.onrender.com/api/health)

## 🛠️ Local Development

### Prerequisites
- Node.js 14+ 
- npm or yarn
- Git

### Quick Start
1. Clone this repository:
   ```bash
   git clone https://github.com/Bechi-cyber/LASAVOT-Backend.git
   cd LASAVOT-Backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Server will be running at http://localhost:3000

### Environment Variables
```env
NODE_ENV=development
PORT=3000
USE_SQLITE=true
SQLITE_DB_PATH=./database/lesavot.db
JWT_SECRET=your_jwt_secret_here
ALLOWED_ORIGINS=http://localhost:8081,http://localhost:8082
```

## 📁 Project Structure
```
/
├── server.js              # Main server file
├── simple-server.js       # Simplified server for testing
├── package.json           # Dependencies and scripts
├── .env                   # Environment configuration
├── routes/                # API route handlers
│   ├── auth.js
│   ├── users.js
│   └── steganography.js
├── utils/                 # Utility modules
│   ├── database.js
│   ├── logger.js
│   └── errorHandler.js
└── database/              # Database files
    └── lesavot.db
```

## 🔗 Frontend Integration

This backend serves the LESAVOT frontend:
- **Repository**: [https://github.com/Bechi-cyber/LASAVOT](https://github.com/Bechi-cyber/LASAVOT)
- **Production**: https://lesavot.vercel.app

## 🔒 Security Features

- JWT token authentication
- Password hashing with bcrypt
- CORS protection
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection headers
- Environment-based secrets

## 📊 API Documentation

### Authentication Endpoints

#### POST /api/v1/auth/signup
Register a new user
```json
{
  "username": "string",
  "email": "string", 
  "password": "string",
  "confirmPassword": "string"
}
```

#### POST /api/v1/auth/signin
Authenticate user
```json
{
  "username": "string",
  "password": "string"
}
```

#### GET /api/v1/auth/me
Get authenticated user info (requires Bearer token)

#### POST /api/v1/auth/logout
Logout user (requires Bearer token)

### Health Check

#### GET /api/health
Server status and metrics
```json
{
  "status": "OK",
  "server": "LESAVOT Backend",
  "timestamp": "2025-07-07T...",
  "users": 0,
  "sessions": 0
}
```

## 🚀 Deployment

### Render Deployment
1. Connect your GitHub repository to Render
2. Set environment variables in Render dashboard
3. Deploy automatically on push to main branch

### Manual Deployment
```bash
npm run build
npm start
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Test API endpoints
npm run test:api
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and support:
- Create an issue on GitHub
- Check the API documentation
- Review the deployment guides

---

**Last Updated**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**Version**: 1.0.0
**Status**: ✅ Production Ready
**API Base**: https://lasavot-backend.onrender.com/api/v1
"@

    $readmeContent | Out-File -FilePath "README.md" -Encoding UTF8

    # Create .env.example file
    Write-ColoredOutput "📝 Creating .env.example..." "Cyan"
    $envExample = @"
# LESAVOT Backend Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Database Configuration
USE_SQLITE=true
SQLITE_DB_PATH=./database/lesavot.db

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:8081,http://localhost:8082,https://lesavot.vercel.app
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Security Configuration
ENABLE_HELMET=true
ENABLE_XSS_PROTECTION=true
SHOW_STACK_TRACES=true
LOG_ERRORS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Session Configuration
SESSION_SECRET=your_session_secret_here
SESSION_TIMEOUT=1800000

# Authentication Configuration
REQUIRE_OTP=false
ALLOW_SIMPLE_LOGIN=true
USE_SESSIONS=true

# Frontend URL
FRONTEND_URL=https://lesavot.vercel.app
"@

    $envExample | Out-File -FilePath ".env.example" -Encoding UTF8

    # Add all files
    Write-ColoredOutput "➕ Adding files to Git..." "Cyan"
    git add .

    # Check if there are changes to commit
    $status = git status --porcelain
    if (-not $status) {
        Write-ColoredOutput "ℹ️ No changes to commit" "Yellow"
    } else {
        # Commit changes
        $commitMessage = "Backend API deployment - Authentication system and simplified server - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        Write-ColoredOutput "💾 Committing changes..." "Cyan"
        git commit -m $commitMessage

        # Push to repository
        Write-ColoredOutput "🚀 Pushing to GitHub..." "Cyan"
        git push -u origin main 2>$null
        if ($LASTEXITCODE -ne 0) {
            # Try master branch if main fails
            git push -u origin master 2>$null
            if ($LASTEXITCODE -ne 0) {
                Write-ColoredOutput "❌ Failed to push to repository" "Red"
                exit 1
            }
        }
        Write-ColoredOutput "✅ Successfully pushed to GitHub!" "Green"
    }

    # Return to original directory
    Set-Location ".."

    # Clean up temp directory
    Write-ColoredOutput "🧹 Cleaning up..." "Yellow"
    Remove-Item -Recurse -Force $tempDir

    Write-ColoredOutput "`n🎉 Backend deployment completed successfully!" "Green"
    Write-ColoredOutput "Repository: https://github.com/Bechi-cyber/LASAVOT-Backend" "Cyan"
    Write-ColoredOutput "Backend API deployed and ready!" "Green"

} catch {
    Write-ColoredOutput "❌ Error during deployment: $_" "Red"
    
    # Clean up on error
    Set-Location ".." -ErrorAction SilentlyContinue
    if (Test-Path $tempDir) {
        Remove-Item -Recurse -Force $tempDir -ErrorAction SilentlyContinue
    }
    
    exit 1
}

Write-ColoredOutput "`nPress any key to exit..." "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
