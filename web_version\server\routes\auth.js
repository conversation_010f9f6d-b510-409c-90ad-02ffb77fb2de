/**
 * Authentication Routes
 *
 * This module defines the routes for authentication operations.
 */

const express = require('express');
const authController = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { authLimiter, passwordResetLimiter } = require('../middleware/rateLimiter');
const { csrfProtection } = require('../middleware/security');
const { validateSignup, validateLogin, validatePasswordUpdate } = require('../middleware/validators');

const router = express.Router();

// Apply CSRF protection to all routes if enabled
if (process.env.ENABLE_CSRF_PROTECTION === 'true') {
  router.use(csrfProtection());
}

// Public routes with rate limiting (simplified - no OTP)
router.post('/signup', authLimiter, validateSignup, authController.signup);
router.post('/login', authLimiter, validateLogin, authController.login);
router.post('/logout', authController.logout);

// Token refresh route
router.post('/refresh-token', authLimiter, authController.refreshToken);

// Password reset and OTP routes removed for simplified authentication

// Protected routes (require authentication)
router.use(protect);
router.get('/me', authController.getCurrentUser);
router.patch('/update-password', validatePasswordUpdate, authController.updatePassword);
router.get('/mfa/status', authController.getMfaStatus); // Returns disabled status

module.exports = router;
