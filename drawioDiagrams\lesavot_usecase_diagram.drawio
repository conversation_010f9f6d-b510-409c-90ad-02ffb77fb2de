<mxfile host="65bd71144e">
    <diagram name="LESAVOT Use Case Diagram" id="usecase-diagram">
        <mxGraphModel dx="703" dy="283" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="system-boundary" value="LESAVOT Multimodal Steganography System" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#000000;strokeWidth=2;dashed=1;verticalAlign=top;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="50" width="700" height="650" as="geometry"/>
                </mxCell>
                <mxCell id="end-user" value="End User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="80" y="200" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="admin" value="Administrator" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="80" y="450" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="guest" value="Guest User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="980" y="150" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="register" value="Register Account" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="240" y="100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="login" value="Login" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="400" y="100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="logout" value="Logout" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="550" y="100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="text-steg" value="Perform Text&#xa;Steganography" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="250" y="180" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="image-steg" value="Perform Image&#xa;Steganography" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="400" y="220" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="audio-steg" value="Perform Audio&#xa;Steganography" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="550" y="180" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="multi-steg" value="Perform Multimodal&#xa;Steganography" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="720" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="upload-files" value="Upload Files" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="250" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="download-results" value="Download Results" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="400" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="manage-history" value="Manage Operation&#xa;History" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="550" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="update-profile" value="Update Profile" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="250" y="400" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="change-password" value="Change Password" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
                    <mxGeometry x="400" y="400" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="manage-users" value="Manage Users" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="250" y="500" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="monitor-system" value="Monitor System" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="400" y="500" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="view-logs" value="View Audit Logs" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="550" y="500" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="configure-system" value="Configure System" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
                    <mxGeometry x="700" y="500" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="view-demo" value="View Demo" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="700" y="100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="access-info" value="Access Platform&#xa;Information" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="700" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="encrypt-data" value="Encrypt Data" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="250" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="decrypt-data" value="Decrypt Data" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="400" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="validate-password" value="Validate Password" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
                    <mxGeometry x="550" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="user-register" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="register" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="240" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-login" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="login" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="390" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-logout" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="logout" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="540" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-text" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="text-steg" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="240" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-image" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="image-steg" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="390" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-audio" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="audio-steg" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="540" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-multi" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="multi-steg" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="690" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-upload" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="upload-files" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="240" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-download" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="download-results" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="390" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-history" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="manage-history" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="540" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-profile" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="update-profile" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="240" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-password" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="end-user" target="change-password" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="220" as="sourcePoint"/>
                        <mxPoint x="390" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="admin-users" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="admin" target="manage-users" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="470" as="sourcePoint"/>
                        <mxPoint x="240" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="admin-monitor" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="admin" target="monitor-system" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="470" as="sourcePoint"/>
                        <mxPoint x="390" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="admin-logs" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="admin" target="view-logs" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="470" as="sourcePoint"/>
                        <mxPoint x="540" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="admin-config" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="admin" target="configure-system" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="470" as="sourcePoint"/>
                        <mxPoint x="690" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="guest-demo" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="guest" target="view-demo" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="970" y="170" as="sourcePoint"/>
                        <mxPoint x="830" y="130" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="guest-info" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.3333333333333333;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="guest" target="access-info" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="970" y="170" as="sourcePoint"/>
                        <mxPoint x="830" y="330" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="include1" value="&lt;&lt;include&gt;&gt;" style="endArrow=open;endSize=12;dashed=1;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="text-steg" target="encrypt-data" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="310" y="270" as="sourcePoint"/>
                        <mxPoint x="310" y="590" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="310" y="500"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="include2" value="&lt;&lt;include&gt;&gt;" style="endArrow=open;endSize=12;dashed=1;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="image-steg" target="decrypt-data" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="460" y="270" as="sourcePoint"/>
                        <mxPoint x="460" y="590" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="460" y="500"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="include3" value="&lt;&lt;include&gt;&gt;" style="endArrow=open;endSize=12;dashed=1;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="audio-steg" target="validate-password" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="610" y="270" as="sourcePoint"/>
                        <mxPoint x="610" y="590" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="610" y="500"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="extend1" value="&lt;&lt;extend&gt;&gt;" style="endArrow=open;endSize=12;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="multi-steg" target="text-steg" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="690" y="230" as="sourcePoint"/>
                        <mxPoint x="380" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="extend2" value="&lt;&lt;extend&gt;&gt;" style="endArrow=open;endSize=12;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="multi-steg" target="image-steg" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="690" y="230" as="sourcePoint"/>
                        <mxPoint x="530" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="extend3" value="&lt;&lt;extend&gt;&gt;" style="endArrow=open;endSize=12;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="multi-steg" target="audio-steg" edge="1">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="690" y="230" as="sourcePoint"/>
                        <mxPoint x="680" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>