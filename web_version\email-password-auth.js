/**
 * Email + Password + OTP Authentication System for LESAVOT
 * Handles traditional signup/signin with OTP verification
 */

class EmailPasswordAuth {
    constructor() {
        this.apiBaseUrl = (window.CONFIG && window.CONFIG.apiBaseUrl) || 'http://localhost:3000/api';
        this.currentUser = null;
        this.isInitialized = false;
        this.init();
    }

    /**
     * Initialize the authentication system
     */
    async init() {
        try {
            this.setupEventListeners();
            this.isInitialized = true;
            console.log('Email + Password + OTP Authentication system initialized');
        } catch (error) {
            console.error('Failed to initialize auth:', error);
        }
    }

    /**
     * Setup event listeners for forms
     */
    setupEventListeners() {
        // Signup form
        const signupBtn = document.getElementById('signupBtn');
        if (signupBtn) {
            signupBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignUp();
            });
        }

        // Signin form
        const signinBtn = document.getElementById('signinBtn');
        if (signinBtn) {
            signinBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignIn();
            });
        }

        // OTP verification buttons
        const verifySignupBtn = document.getElementById('verifySignupBtn');
        if (verifySignupBtn) {
            verifySignupBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSignupOTPVerification();
            });
        }

        const verifySigninBtn = document.getElementById('verifySigninBtn');
        if (verifySigninBtn) {
            verifySigninBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSigninOTPVerification();
            });
        }

        // Form switching
        const showSignUpBtn = document.getElementById('showSignUpBtn');
        const showSignInBtn = document.getElementById('showSignInBtn');
        
        if (showSignUpBtn) {
            showSignUpBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSignupForm();
            });
        }

        if (showSignInBtn) {
            showSignInBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSigninForm();
            });
        }
    }

    /**
     * Handle signup process
     */
    async handleSignUp() {
        try {
            const email = document.getElementById('signupEmail')?.value?.trim();
            const password = document.getElementById('signupPassword')?.value;
            const confirmPassword = document.getElementById('confirmPassword')?.value;
            const agreeTerms = document.getElementById('agreeTerms')?.checked;

            // Validation
            if (!email || !this.isValidEmail(email)) {
                this.showMessage('Please enter a valid email address', 'error');
                return;
            }

            if (!password || password.length < 6) {
                this.showMessage('Password must be at least 6 characters long', 'error');
                return;
            }

            if (password !== confirmPassword) {
                this.showMessage('Passwords do not match', 'error');
                return;
            }

            if (!agreeTerms) {
                this.showMessage('Please agree to the Terms of Service and Privacy Policy', 'error');
                return;
            }

            this.showLoading('Creating account...');

            const response = await fetch(`${this.apiBaseUrl}/auth/signup/request`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password, confirmPassword })
            });

            const data = await response.json();

            if (response.ok) {
                this.showMessage('Account created! Please check your email for verification code.', 'success');
                this.showSignupOTPSection();
            } else {
                this.showMessage(data.message || 'Failed to create account', 'error');
            }

        } catch (error) {
            console.error('Sign up error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle signin process
     */
    async handleSignIn() {
        try {
            const email = document.getElementById('signinEmail')?.value?.trim();
            const password = document.getElementById('signinPassword')?.value;

            // Validation
            if (!email || !this.isValidEmail(email)) {
                this.showMessage('Please enter a valid email address', 'error');
                return;
            }

            if (!password) {
                this.showMessage('Please enter your password', 'error');
                return;
            }

            this.showLoading('Verifying credentials...');

            const response = await fetch(`${this.apiBaseUrl}/auth/signin/request`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password })
            });

            const data = await response.json();

            if (response.ok) {
                this.showMessage('Credentials verified! Please check your email for verification code.', 'success');
                this.showSigninOTPSection();
            } else {
                this.showMessage(data.message || 'Invalid credentials', 'error');
            }

        } catch (error) {
            console.error('Sign in error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle signup OTP verification
     */
    async handleSignupOTPVerification() {
        try {
            const email = document.getElementById('signupEmail')?.value?.trim();
            const otp = document.getElementById('signupOtp')?.value?.trim();

            if (!otp || otp.length !== 6 || !/^\d{6}$/.test(otp)) {
                this.showMessage('Please enter a valid 6-digit code', 'error');
                return;
            }

            this.showLoading('Verifying code...');

            const response = await fetch(`${this.apiBaseUrl}/auth/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, otp })
            });

            const data = await response.json();

            if (response.ok) {
                this.currentUser = data.user;
                this.storeToken(data.sessionToken);
                
                this.showMessage('Account created successfully! Welcome to LESAVOT!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/index.html';
                }, 2000);
                
            } else {
                this.showMessage(data.message || 'Verification failed', 'error');
            }

        } catch (error) {
            console.error('OTP verification error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle signin OTP verification
     */
    async handleSigninOTPVerification() {
        try {
            const email = document.getElementById('signinEmail')?.value?.trim();
            const otp = document.getElementById('signinOtp')?.value?.trim();

            if (!otp || otp.length !== 6 || !/^\d{6}$/.test(otp)) {
                this.showMessage('Please enter a valid 6-digit code', 'error');
                return;
            }

            this.showLoading('Signing in...');

            const response = await fetch(`${this.apiBaseUrl}/auth/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, otp })
            });

            const data = await response.json();

            if (response.ok) {
                this.currentUser = data.user;
                this.storeToken(data.sessionToken);
                
                this.showMessage('Sign in successful! Welcome back!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/index.html';
                }, 2000);
                
            } else {
                this.showMessage(data.message || 'Verification failed', 'error');
            }

        } catch (error) {
            console.error('OTP verification error:', error);
            this.showMessage('Network error. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Show signup OTP section
     */
    showSignupOTPSection() {
        const otpSection = document.getElementById('signupOtpSection');
        if (otpSection) {
            otpSection.style.display = 'block';
            const otpInput = document.getElementById('signupOtp');
            if (otpInput) {
                otpInput.focus();
            }
        }
    }

    /**
     * Show signin OTP section
     */
    showSigninOTPSection() {
        const otpSection = document.getElementById('signinOtpSection');
        if (otpSection) {
            otpSection.style.display = 'block';
            const otpInput = document.getElementById('signinOtp');
            if (otpInput) {
                otpInput.focus();
            }
        }
    }

    /**
     * Show signup form
     */
    showSignupForm() {
        const signupForm = document.getElementById('signupForm');
        const signinForm = document.getElementById('signinForm');
        
        if (signupForm && signinForm) {
            signupForm.classList.add('active');
            signinForm.classList.remove('active');
        }
    }

    /**
     * Show signin form
     */
    showSigninForm() {
        const signupForm = document.getElementById('signupForm');
        const signinForm = document.getElementById('signinForm');
        
        if (signupForm && signinForm) {
            signinForm.classList.add('active');
            signupForm.classList.remove('active');
        }
    }

    /**
     * Utility functions
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        const messageDiv = document.getElementById('authMessage');
        if (messageDiv) {
            messageDiv.textContent = message;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }

    showLoading(message = 'Loading...') {
        const loadingDiv = document.getElementById('loadingMessage');
        if (loadingDiv) {
            loadingDiv.textContent = message;
            loadingDiv.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingDiv = document.getElementById('loadingMessage');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    storeToken(token) {
        localStorage.setItem('lesavot_session_token', token);
    }

    getStoredToken() {
        return localStorage.getItem('lesavot_session_token');
    }

    removeStoredToken() {
        localStorage.removeItem('lesavot_session_token');
    }

    isAuthenticated() {
        return !!this.currentUser && !!this.getStoredToken();
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.emailPasswordAuth = new EmailPasswordAuth();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmailPasswordAuth;
}
