/**
 * LESAVOT - User Authentication System
 *
 * This module provides user authentication functionality with OTP verification
 * and integrates with the backend authentication system.
 */

// User Authentication Class
class UserAuth {
    constructor() {
        this.apiBaseUrl = (window.CONFIG && window.CONFIG.apiBaseUrl) || 'http://localhost:3000/api';
        this.currentUser = null;
        this.sessionToken = null;
        this.init();
    }

    async init() {
        // Check if user is already authenticated
        this.sessionToken = this.getStoredToken();
        if (this.sessionToken) {
            await this.checkAuthStatus();
        }
    }

    /**
     * Check authentication status with backend
     */
    async checkAuthStatus() {
        try {
            if (!this.sessionToken) {
                return false;
            }

            const response = await fetch(`${this.apiBaseUrl}/auth/status`, {
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                return true;
            } else {
                this.logout();
                return false;
            }
        } catch (error) {
            console.error('Auth status check failed:', error);
            this.logout();
            return false;
        }
    }

    /**
     * Get current user profile
     */
    async getUserProfile() {
        try {
            if (!this.sessionToken) {
                throw new Error('Not authenticated');
            }

            const response = await fetch(`${this.apiBaseUrl}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                return data.user;
            } else {
                throw new Error('Failed to get profile');
            }
        } catch (error) {
            console.error('Get profile error:', error);
            throw error;
        }
    }

    /**
     * Update user profile
     */
    async updateProfile(updates) {
        try {
            if (!this.sessionToken) {
                throw new Error('Not authenticated');
            }

            const response = await fetch(`${this.apiBaseUrl}/auth/profile`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.sessionToken}`
                },
                body: JSON.stringify(updates)
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                return data.user;
            } else {
                const error = await response.json();
                throw new Error(error.message || 'Failed to update profile');
            }
        } catch (error) {
            console.error('Update profile error:', error);
            throw error;
        }
    }

    /**
     * Log out the current user
     */
    async logout() {
        try {
            if (this.sessionToken) {
                await fetch(`${this.apiBaseUrl}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.sessionToken}`
                    }
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.currentUser = null;
            this.sessionToken = null;
            this.removeStoredToken();
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!this.currentUser && !!this.sessionToken;
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Store session token
     */
    storeToken(token) {
        this.sessionToken = token;
        localStorage.setItem('lesavot_session_token', token);
    }

    /**
     * Get stored token
     */
    getStoredToken() {
        return localStorage.getItem('lesavot_session_token');
    }

    /**
     * Remove stored token
     */
    removeStoredToken() {
        localStorage.removeItem('lesavot_session_token');
    }

    /**
     * Redirect to auth page if not authenticated
     */
    requireAuth() {
        if (!this.isAuthenticated()) {
            window.location.href = '/auth.html';
            return false;
        }
        return true;
    }

    /**
     * Redirect to main page if already authenticated
     */
    redirectIfAuthenticated() {
        if (this.isAuthenticated()) {
            window.location.href = '/index.html';
            return true;
        }
        return false;
    }

    /**
     * Make authenticated API request
     */
    async makeAuthenticatedRequest(url, options = {}) {
        if (!this.sessionToken) {
            throw new Error('Not authenticated');
        }

        const headers = {
            'Authorization': `Bearer ${this.sessionToken}`,
            ...options.headers
        };

        const response = await fetch(url, {
            ...options,
            headers
        });

        if (response.status === 401) {
            // Token expired or invalid
            this.logout();
            throw new Error('Authentication expired. Please sign in again.');
        }

        return response;
    }

    /**
     * Legacy compatibility methods
     */
    isLoggedIn() {
        return this.isAuthenticated();
    }

    login(email, password) {
        // Legacy method - redirect to OTP auth
        console.warn('Legacy login method called. Redirecting to OTP authentication.');
        window.location.href = '/auth.html';
        return { success: false, message: 'Please use OTP authentication' };
    }

    register(userData) {
        // Legacy method - redirect to OTP auth
        console.warn('Legacy register method called. Redirecting to OTP authentication.');
        window.location.href = '/auth.html';
        return { success: false, message: 'Please use OTP authentication' };
    }
}

// Create global instance
window.userAuth = new UserAuth();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserAuth;
}
