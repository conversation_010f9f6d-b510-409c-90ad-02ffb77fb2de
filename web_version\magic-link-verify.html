<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT | Magic Link Verification</title>
    <link rel="stylesheet" href="text_stego.css">
    <link rel="stylesheet" href="cybersecurity_elements.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="snowy_raindrops.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .verification-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .verification-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .verification-header {
            margin-bottom: 30px;
        }
        
        .verification-header i {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .verification-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .verification-status {
            margin: 30px 0;
            padding: 20px;
            border-radius: 10px;
            font-size: 1.1rem;
        }
        
        .status-loading {
            background: #e3f2fd;
            color: #1976d2;
            border: 2px solid #bbdefb;
        }
        
        .status-success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #a5d6a7;
        }
        
        .status-error {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #ef9a9a;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .action-buttons {
            margin-top: 30px;
        }
        
        .action-buttons .btn {
            margin: 10px;
            padding: 12px 30px;
            font-size: 1rem;
        }
        
        .magic-link-status {
            display: none;
        }
        
        .magic-link-status.show {
            display: block;
        }
    </style>
</head>
<body>
    <div id="app">
        <header>
            <div class="snowflake-container">
                <!-- Animated background elements -->
                <div class="snowflake snow-1">│</div>
                <div class="snowflake snow-2">│</div>
                <div class="snowflake snow-3">│</div>
                <div class="snowflake snow-4">│</div>
                <div class="snowflake snow-5">│</div>
                <div class="snowflake snow-6">│</div>
                <div class="snowflake snow-7">│</div>
                <div class="snowflake snow-8">│</div>
                <div class="snowflake snow-9">│</div>
                <div class="snowflake snow-10">│</div>
            </div>
        </header>

        <main>
            <div class="verification-container">
                <div class="verification-card">
                    <div class="verification-header">
                        <i class="fas fa-magic" id="headerIcon"></i>
                        <h1>Magic Link Verification</h1>
                        <p>Verifying your authentication...</p>
                    </div>

                    <!-- Loading State -->
                    <div id="loadingStatus" class="verification-status status-loading">
                        <div class="loading-spinner"></div>
                        <span>Verifying your magic link...</span>
                    </div>

                    <!-- Success State -->
                    <div id="successStatus" class="verification-status status-success magic-link-status">
                        <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 15px; display: block;"></i>
                        <strong>Authentication Successful!</strong>
                        <p>You have been successfully signed in to LESAVOT.</p>
                        <p>Redirecting you to the platform...</p>
                    </div>

                    <!-- Error State -->
                    <div id="errorStatus" class="verification-status status-error magic-link-status">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px; display: block;"></i>
                        <strong>Verification Failed</strong>
                        <p id="errorMessage">The magic link is invalid or has expired.</p>
                        <p>Please request a new magic link to continue.</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button id="continueBtn" class="btn btn-primary magic-link-status" style="display: none;">
                            <i class="fas fa-arrow-right"></i> Continue to LESAVOT
                        </button>
                        <button id="requestNewBtn" class="btn btn-outline magic-link-status" style="display: none;">
                            <i class="fas fa-redo"></i> Request New Magic Link
                        </button>
                        <a href="index.html" class="btn btn-outline">
                            <i class="fas fa-home"></i> Back to Homepage
                        </a>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; 2025 LESAVOT Advanced Security Systems. All rights reserved.</p>
                <p>Enterprise-Grade Multimodal Steganographic Information Protection Platform</p>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="api-client.js"></script>
    <script>
        class MagicLinkVerifier {
            constructor() {
                this.init();
            }

            init() {
                // Get URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const token = urlParams.get('token');
                const email = urlParams.get('email');

                if (!token || !email) {
                    this.showError('Invalid magic link. Missing required parameters.');
                    return;
                }

                // Verify the magic link
                this.verifyMagicLink(token, email);
            }

            async verifyMagicLink(token, email) {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/magic-auth/verify-magic-link`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ token, email })
                    });

                    const data = await response.json();

                    if (response.ok && data.status === 'success') {
                        // Store session token
                        localStorage.setItem('sessionToken', data.data.token);
                        localStorage.setItem('user', JSON.stringify(data.data.user));
                        
                        this.showSuccess();
                        
                        // Redirect after 3 seconds
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 3000);
                    } else {
                        this.showError(data.message || 'Verification failed');
                    }
                } catch (error) {
                    console.error('Magic link verification error:', error);
                    this.showError('Network error. Please check your connection and try again.');
                }
            }

            showSuccess() {
                document.getElementById('loadingStatus').style.display = 'none';
                document.getElementById('successStatus').classList.add('show');
                document.getElementById('continueBtn').style.display = 'inline-block';
                
                // Update header
                document.getElementById('headerIcon').className = 'fas fa-check-circle';
                document.getElementById('headerIcon').style.color = '#2e7d32';
            }

            showError(message) {
                document.getElementById('loadingStatus').style.display = 'none';
                document.getElementById('errorStatus').classList.add('show');
                document.getElementById('errorMessage').textContent = message;
                document.getElementById('requestNewBtn').style.display = 'inline-block';
                
                // Update header
                document.getElementById('headerIcon').className = 'fas fa-exclamation-triangle';
                document.getElementById('headerIcon').style.color = '#c62828';
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            new MagicLinkVerifier();

            // Continue button
            document.getElementById('continueBtn').addEventListener('click', () => {
                window.location.href = 'index.html';
            });

            // Request new magic link button
            document.getElementById('requestNewBtn').addEventListener('click', () => {
                window.location.href = 'auth.html';
            });
        });
    </script>
</body>
</html>
