{"cause":{"beforeHandshake":false,"errorLabelSet":{}},"errorLabelSet":{},"level":"error","message":"Failed to connect to MongoDB: 145F0100:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\rec_layer_s3.c:1590:SSL alert number 80\n","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-eti5dcf-shard-00-00.sh5pgu3.mongodb.net:27017":{"$clusterTime":null,"address":"ac-eti5dcf-shard-00-00.sh5pgu3.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":1345738340,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-eti5dcf-shard-00-01.sh5pgu3.mongodb.net:27017":{"$clusterTime":null,"address":"ac-eti5dcf-shard-00-01.sh5pgu3.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":1345738268,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-eti5dcf-shard-00-02.sh5pgu3.mongodb.net:27017":{"$clusterTime":null,"address":"ac-eti5dcf-shard-00-02.sh5pgu3.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":1345738319,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l0qihv-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"lesavot-api","stack":"MongoServerSelectionError: 145F0100:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\rec_layer_s3.c:1590:SSL alert number 80\n\n    at Topology.selectServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n    at async Topology._connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n    at async Topology.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n    at async topologyConnect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n    at async MongoClient._connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n    at async MongoClient.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n    at async Database.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:46:7)\n    at async testMongoDBConnection (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-mongodb.js:21:5)","timestamp":"2025-06-27 19:30:16:3016"}
{"code":"23505","constraint":"users_username_key","detail":"Key (username)=(testuser1_1751050675560) already exists.","file":"nbtinsert.c","length":224,"level":"error","line":"666","message":"Error saving user: duplicate key value violates unique constraint \"users_username_key\"","name":"error","routine":"_bt_check_unique","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: duplicate key value violates unique constraint \"users_username_key\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async User.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:88:24)\n    at async testValidationAndEdgeCases (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:308:7)\n    at async runUserModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:52:5)","table":"users","timestamp":"2025-06-27 19:58:20:5820"}
{"code":"23505","constraint":"users_email_key","detail":"Key (email)=(<EMAIL>) already exists.","file":"nbtinsert.c","length":227,"level":"error","line":"666","message":"Error saving user: duplicate key value violates unique constraint \"users_email_key\"","name":"error","routine":"_bt_check_unique","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: duplicate key value violates unique constraint \"users_email_key\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async User.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:88:24)\n    at async testValidationAndEdgeCases (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:326:7)\n    at async runUserModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:52:5)","table":"users","timestamp":"2025-06-27 19:58:21:5821"}
{"code":"23503","constraint":"sessions_user_id_fkey","detail":"Key (user_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":254,"level":"error","line":"2608","message":"Error saving session: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async Session.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Session.js:52:24)\n    at async testSessionCreation (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:85:21)\n    at async runSessionModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:37:22)","table":"sessions","timestamp":"2025-06-27 20:03:34:334"}
{"code":"23503","constraint":"sessions_user_id_fkey","detail":"Key (user_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":254,"level":"error","line":"2608","message":"Error saving session: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async Session.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Session.js:52:24)\n    at async testSessionCreation (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:85:21)\n    at async runSessionModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:37:22)","table":"sessions","timestamp":"2025-06-27 20:04:37:437"}
{"code":"42703","file":"parse_target.c","length":147,"level":"error","line":"1066","message":"Error saving steganography operation: column \"media_type\" of relation \"steganography_operations\" does not exist","name":"error","position":"88","routine":"checkInsertTargets","service":"lesavot-api","severity":"ERROR","stack":"error: column \"media_type\" of relation \"steganography_operations\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async SteganographyOperation.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\SteganographyOperation.js:66:24)\n    at async testOperationCreation (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:86:7)\n    at async runSteganographyOperationModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:45:5)","timestamp":"2025-06-27 20:14:33:1433"}
{"level":"error","message":"Error finding operation by ID: \"[object Object]\" is not valid JSON","service":"lesavot-api","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at SteganographyOperation.findById (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\SteganographyOperation.js:103:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testOperationLookupMethods (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:122:23)\n    at async runSteganographyOperationModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:44:5)","timestamp":"2025-06-27 20:21:58:2158"}
{"code":"42703","file":"parse_relation.c","length":110,"level":"error","line":"3722","message":"Error getting global stats: column \"media_type\" does not exist","name":"error","position":"17","routine":"errorMissingColumn","service":"lesavot-api","severity":"ERROR","stack":"error: column \"media_type\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async SteganographyOperation.getGlobalStats (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\SteganographyOperation.js:310:31)\n    at async testOperationStatistics (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:282:25)\n    at async runSteganographyOperationModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:47:5)","timestamp":"2025-06-27 20:27:05:275"}
{"code":"23503","constraint":"metrics_user_id_fkey","detail":"Key (user_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":250,"level":"error","line":"2608","message":"Error saving metrics: insert or update on table \"metrics\" violates foreign key constraint \"metrics_user_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: insert or update on table \"metrics\" violates foreign key constraint \"metrics_user_id_fkey\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async Metrics.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Metrics.js:46:24)\n    at async Metrics.recordPageView (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Metrics.js:87:7)\n    at async testMetricsModel (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:18:22)","table":"metrics","timestamp":"2025-06-27 20:39:56:3956"}
{"code":"42703","file":"parse_relation.c","length":121,"level":"error","line":"3722","message":"Error finding user by username: column \"password_reset_token\" does not exist","name":"error","position":"157","routine":"errorMissingColumn","service":"lesavot-api","severity":"ERROR","stack":"error: column \"password_reset_token\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async User.findByUsername (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:120:22)\n    at async testAuthController (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-auth-controller.js:39:25)","timestamp":"2025-06-27 20:53:15:5315"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:09:13:913","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:14:42:1442","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:131:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:14:44:1444","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Error finding user by ID: Database not connected","service":"lesavot-api","stack":"Error: Database not connected\n    at Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:207:13)\n    at User.findById (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:162:37)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:42:36)\n    at testAuthMiddleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-auth-middleware.js:92:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:16:41:1641"}
{"level":"error","message":"Authentication middleware error: Database not connected","service":"lesavot-api","stack":"Error: Database not connected\n    at Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:207:13)\n    at User.findById (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:162:37)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:42:36)\n    at testAuthMiddleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-auth-middleware.js:92:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:16:41:1641"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e52bd5sm3589026f8f.59 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e52bd5sm3589026f8f.59 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e52bd5sm3589026f8f.59 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:17:00:170"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:17:00:170","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:17:02:172","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538234b1b9sm88644525e9.11 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538234b1b9sm88644525e9.11 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538234b1b9sm88644525e9.11 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:19:45:1945"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:19:45:1945","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:19:46:1946","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e5f8b6sm3628804f8f.91 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e5f8b6sm3628804f8f.91 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e5f8b6sm3628804f8f.91 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:20:36:2036"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:20:36:2036","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:20:38:2038","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Save operation not implemented. Database logic required.","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Save operation not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:20:40:2040","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Get history not implemented. Database logic required.","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Get history not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:17:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:20:41:2041","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-453823b6d50sm91865785e9.30 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-453823b6d50sm91865785e9.30 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-453823b6d50sm91865785e9.30 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:21:37:2137"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:21:37:2137","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:21:38:2138","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Save operation not implemented. Database logic required.","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Save operation not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:21:40:2140","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Get history not implemented. Database logic required.","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Get history not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:17:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:21:41:2141","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538a3b3213sm59112605e9.18 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538a3b3213sm59112605e9.18 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538a3b3213sm59112605e9.18 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:22:46:2246"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:22:46:2246","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:22:48:2248","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Save operation not implemented. Database logic required.","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Save operation not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:22:50:2250","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Get history not implemented. Database logic required.","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Get history not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:17:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:22:50:2250","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:40:28:4028","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid mode. Must be encrypt or decrypt","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Invalid mode. Must be encrypt or decrypt\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:30:17\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:40:30:4030","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error fetching steganography history: relation \"stego_history\" does not exist","name":"error","position":"86","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:94:20","timestamp":"2025-06-27 21:40:33:4033"}
{"ip":"::1","level":"error","message":"Error: Error fetching history","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error fetching history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:125:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:40:33:4033","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:41:55:4155","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error saving steganography operation: relation \"stego_history\" does not exist","name":"error","position":"20","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:48:20","timestamp":"2025-06-27 21:41:59:4159"}
{"ip":"::1","level":"error","message":"Error: Error saving operation to history","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error saving operation to history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:62:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:41:59:4159","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error fetching steganography history: relation \"stego_history\" does not exist","name":"error","position":"86","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:94:20","timestamp":"2025-06-27 21:42:01:421"}
{"ip":"::1","level":"error","message":"Error: Error fetching history","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error fetching history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:125:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:42:01:421","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:46:27:4627","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error saving steganography operation: relation \"stego_history\" does not exist","name":"error","position":"20","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:48:20","timestamp":"2025-06-27 21:46:31:4631"}
{"ip":"::1","level":"error","message":"Error: Error saving operation to history","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error saving operation to history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:62:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:46:31:4631","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error fetching steganography history: relation \"stego_history\" does not exist","name":"error","position":"86","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:94:20","timestamp":"2025-06-27 21:46:34:4634"}
{"ip":"::1","level":"error","message":"Error: Error fetching history","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error fetching history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:125:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:46:34:4634","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:47:27:4727","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:48:49:4849","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"cause":{},"level":"error","message":"Failed to connect to PostgreSQL: Connection terminated due to connection timeout","service":"lesavot-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:51:22)\n    at async testDatabaseConnection (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-postgresql.js:76:5)\n    at async runTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-postgresql.js:31:5)","timestamp":"2025-06-30 07:06:27:627"}
{"cause":{},"level":"error","message":"Failed to initialize PostgreSQL connection pool: Connection terminated due to connection timeout","service":"lesavot-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 07:07:08:78"}
{"cause":{},"level":"error","message":"Failed to connect to PostgreSQL: Connection terminated due to connection timeout","service":"lesavot-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:51:22)\n    at async initializeDatabase (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:116:5)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:301:5)","timestamp":"2025-06-30 07:07:08:78"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 07:57:26:5726","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 07:59:19:5919"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 08:04:29:429"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:304:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-02 03:18:46:1846"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:23:29:2329","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:24:49:2449","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:24:52:2452","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:24:52:2452"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:25:42:2542","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:25:46:2546","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:25:46:2546"}
{"ip":"::1","level":"error","message":"Error: Too many authentication attempts, please try again later","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Too many authentication attempts, please try again later\n    at Object.defaultHandler [as handler] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\rateLimiter.js:41:12)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-02 03:35:06:356","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Too many authentication attempts, please try again later","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Too many authentication attempts, please try again later\n    at Object.defaultHandler [as handler] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\rateLimiter.js:41:12)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-02 03:36:07:367","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:37:16:3716","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Too many authentication attempts, please try again later","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Too many authentication attempts, please try again later\n    at Object.defaultHandler [as handler] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\rateLimiter.js:41:12)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-02 03:38:18:3818","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:39:55:3955","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:40:10:4010","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:40:13:4013","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:41:18:4118","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:41:23:4123","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:41:24:4124","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:45:31:4531","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:45:35:4535","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:45:37:4537","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:51:31:5131","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:51:36:5136","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:51:37:5137","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Error generating JWT token: JWT signing key not configured for algorithm HS256","service":"lesavot-api","stack":"Error: JWT signing key not configured for algorithm HS256\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:72:13)\n    at [eval]:1:187\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:45:14:4514"}
{"level":"error","message":"Error generating JWT token: JWT secret not configured for algorithm HS256","service":"lesavot-api","stack":"Error: JWT secret not configured for algorithm HS256\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:77:15)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:46:20:4620"}
{"level":"error","message":"Error generating JWT token: Bad \"options.audience\" option. The payload already has an \"aud\" property.","service":"lesavot-api","stack":"Error: Bad \"options.audience\" option. The payload already has an \"aud\" property.\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:221:24\n    at Array.forEach (<anonymous>)\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:217:35)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:85:23)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)","timestamp":"2025-07-04 03:47:47:4747"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-04 04:01:52:152"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-04 04:02:19:219"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-04 04:04:58:458"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-04 04:15:46:1546"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-04 04:16:09:169"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:306:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-04 04:31:05:315"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:36:23:3623","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:43:21:4321","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 04:53:02:532"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 04:53:02:532"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/login","service":"lesavot-api","timestamp":"2025-07-04 04:53:02:532","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:53:53:5353"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/simple-login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:53:53:5353"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:53:53:5353","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:55:06:556"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/simple-login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:55:06:556"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:55:06:556","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 04:58:43:5843"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 04:58:43:5843"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:58:43:5843","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 05:55:44:5544"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 05:55:44:5544"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:656:17","timestamp":"2025-07-04 05:55:44:5544","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 05:56:00:560"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 05:56:00:560"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 05:56:00:560","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 05:57:43:5743","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:623:19","timestamp":"2025-07-04 05:57:56:5756","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 06:00:50:050","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 06:02:53:253","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 06:04:41:441"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 06:04:41:441"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:656:17","timestamp":"2025-07-04 06:04:41:441","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:48:03:483"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:48:03:483"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:669:17","timestamp":"2025-07-04 10:48:03:483","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:51:35:5135"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:51:35:5135"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:669:17","timestamp":"2025-07-04 10:51:35:5135","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Username already exists","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Username already exists\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:296:17","timestamp":"2025-07-04 10:51:46:5146","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Username already exists","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Username already exists\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:296:17","timestamp":"2025-07-04 10:51:56:5156","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:54:05:545"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:54:05:545"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:670:17","timestamp":"2025-07-04 10:54:05:545","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421"}
{"email":"<EMAIL>","error":"User already exists with this email","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: no such column: display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: no such column: display_name\n--> in Database#all('\\n' +\n  '          UPDATE users \\n' +\n  '          SET email = ?, display_name = ?, is_verified = ?, \\n' +\n  '              magic_link_token = ?, magic_link_expires = ?,\\n' +\n  '              failed_attempts = ?, account_locked_until = ?,\\n' +\n  '              last_login = ?, updated_at = CURRENT_TIMESTAMP\\n' +\n  '          WHERE id = ?\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  '',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.4Fusjtss7xDoc_Tw2eA3YJy-n2aw9uuyJRC7W5NIZBE',\n  2025-07-04T10:57:17.201Z,\n  0,\n  null,\n  null,\n  2\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:102:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:132:22)","timestamp":"2025-07-04 11:42:17:4217"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"0":"<EMAIL>","1":"","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.4Fusjtss7xDoc_Tw2eA3YJy-n2aw9uuyJRC7W5NIZBE","4":"2025-07-04T10:57:17.201Z","5":0,"6":null,"7":null,"8":2,"level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: no such column: display_name","ip":"::1","level":"error","message":"Signin magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signin/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:160:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:42:17:4217","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: no such column: display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: no such column: display_name\n--> in Database#all('\\n' +\n  '          UPDATE users \\n' +\n  '          SET email = ?, display_name = ?, is_verified = ?, \\n' +\n  '              magic_link_token = ?, magic_link_expires = ?,\\n' +\n  '              failed_attempts = ?, account_locked_until = ?,\\n' +\n  '              last_login = ?, updated_at = CURRENT_TIMESTAMP\\n' +\n  '          WHERE id = ?\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  '',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.GybRm7ak2l_pkmFUQ2oVIgO7GkQ92KL93QjtPsIS7no',\n  2025-07-04T10:58:34.208Z,\n  0,\n  null,\n  null,\n  2\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:102:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:132:22)","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"0":"<EMAIL>","1":"","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.GybRm7ak2l_pkmFUQ2oVIgO7GkQ92KL93QjtPsIS7no","4":"2025-07-04T10:58:34.208Z","5":0,"6":null,"7":null,"8":2,"level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: no such column: display_name","ip":"::1","level":"error","message":"Signin magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signin/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:160:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:43:34:4334","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: no such column: display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: no such column: display_name\n--> in Database#all('\\n' +\n  '          UPDATE users \\n' +\n  '          SET email = ?, display_name = ?, is_verified = ?, \\n' +\n  '              magic_link_token = ?, magic_link_expires = ?,\\n' +\n  '              failed_attempts = ?, account_locked_until = ?,\\n' +\n  '              last_login = ?, updated_at = CURRENT_TIMESTAMP\\n' +\n  '          WHERE id = ?\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  '',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.BcpkMp3m6_Blcmh-ZVQ0fuN9fD5Pq0cl4-DoqkWckxU',\n  2025-07-04T10:59:11.983Z,\n  0,\n  null,\n  null,\n  2\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:102:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:132:22)","timestamp":"2025-07-04 11:44:11:4411"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"0":"<EMAIL>","1":"","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.BcpkMp3m6_Blcmh-ZVQ0fuN9fD5Pq0cl4-DoqkWckxU","4":"2025-07-04T10:59:11.983Z","5":0,"6":null,"7":null,"8":2,"level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: no such column: display_name","ip":"::1","level":"error","message":"Signin magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signin/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:160:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:44:11:4411","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: table users has no column named display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.rNb7nQUvub0CoG8dqyAXyEORH07CzG5k6CunZvsKNqs',\n  2025-07-04T10:59:36.079Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.rNb7nQUvub0CoG8dqyAXyEORH07CzG5k6CunZvsKNqs","4":"2025-07-04T10:59:36.079Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: table users has no column named display_name","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signup/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:97:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:44:36:4436","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: table users has no column named display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.SlUCWac0g79yiG10B8K2ZL5LT8xqrU2d72Rdv7RU7q4',\n  2025-07-06T05:39:00.170Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-06 06:24:00:240"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.SlUCWac0g79yiG10B8K2ZL5LT8xqrU2d72Rdv7RU7q4","4":"2025-07-06T05:39:00.170Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: table users has no column named display_name","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.SlUCWac0g79yiG10B8K2ZL5LT8xqrU2d72Rdv7RU7q4',\n  2025-07-06T05:39:00.170Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-06 06:24:00:240"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: table users has no column named display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o5xSfB3ZExRLVUrgY2zVLzBk5OVpLlAsycGola9YoFk',\n  2025-07-07T10:16:10.660Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:01:10:110"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o5xSfB3ZExRLVUrgY2zVLzBk5OVpLlAsycGola9YoFk","4":"2025-07-07T10:16:10.660Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: table users has no column named display_name","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o5xSfB3ZExRLVUrgY2zVLzBk5OVpLlAsycGola9YoFk',\n  2025-07-07T10:16:10.660Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:01:10:110"}
{"__augmented":true,"code":"SQLITE_CONSTRAINT","errno":19,"level":"error","message":"SQLite query error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.Ml9Nf2jYadc8y7D-vNTNsumDlfK3w4dV3cO5R2URyeM',\n  2025-07-07T10:19:24.560Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:04:24:424"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.Ml9Nf2jYadc8y7D-vNTNsumDlfK3w4dV3cO5R2URyeM","4":"2025-07-07T10:19:24.560Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"email":"<EMAIL>","error":"SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.Ml9Nf2jYadc8y7D-vNTNsumDlfK3w4dV3cO5R2URyeM',\n  2025-07-07T10:19:24.560Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:04:24:424"}
{"__augmented":true,"code":"SQLITE_CONSTRAINT","errno":19,"level":"error","message":"SQLite query error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser2',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.5a5o3rOu6kF0GTkAUk8_2XLpdy7AFxN___UPalogIc0',\n  2025-07-07T10:21:12.106Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"0":"<EMAIL>","1":"newuser2","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.5a5o3rOu6kF0GTkAUk8_2XLpdy7AFxN___UPalogIc0","4":"2025-07-07T10:21:12.106Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"email":"<EMAIL>","error":"SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser2',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.5a5o3rOu6kF0GTkAUk8_2XLpdy7AFxN___UPalogIc0',\n  2025-07-07T10:21:12.106Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:08:50:850"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:08:50:850"}
{"ip":"::1","level":"error","message":"Error: Authentication failed. Please try again.","method":"POST","path":"/api/magic-auth/verify-magic-link","service":"lesavot-api","stack":"Error: Authentication failed. Please try again.\n    at verifyMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:249:12)","timestamp":"2025-07-07 11:08:50:850","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:10:19:1019"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:10:19:1019"}
{"ip":"::1","level":"error","message":"Error: Authentication failed. Please try again.","method":"POST","path":"/api/magic-auth/verify-magic-link","service":"lesavot-api","stack":"Error: Authentication failed. Please try again.\n    at verifyMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:249:12)","timestamp":"2025-07-07 11:10:19:1019","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:14:46:1446"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:14:46:1446"}
{"ip":"::1","level":"error","message":"Error: Authentication failed. Please try again.","method":"POST","path":"/api/magic-auth/verify-magic-link","service":"lesavot-api","stack":"Error: Authentication failed. Please try again.\n    at verifyMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:249:12)","timestamp":"2025-07-07 11:14:46:1446","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:21:03:213"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:21:03:213"}
{"ip":"::1","level":"error","message":"Error: Authentication failed. Please try again.","method":"POST","path":"/api/magic-auth/verify-magic-link","service":"lesavot-api","stack":"Error: Authentication failed. Please try again.\n    at verifyMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:249:12)","timestamp":"2025-07-07 11:21:03:213","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:23:20:2320"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:23:20:2320"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:24:59:2459"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:24:59:2459"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:27:30:2730"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:27:30:2730"}
{"__augmented":true,"code":"SQLITE_CONSTRAINT","errno":19,"level":"error","message":"SQLite query error: SQLITE_CONSTRAINT: UNIQUE constraint failed: users.username","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: users.username\n--> in Statement#all([\n  'testuser',\n  '<EMAIL>',\n  'testuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.oPDVbEgyPUdpcvvVRO0UnWxZcC1y298PjKeMLjjteBU',\n  2025-07-07T10:45:31.149Z,\n  'magic_link_auth'\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:125:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:30:31:3031"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:30:31:3031"}
{"0":"testuser","1":"<EMAIL>","2":"testuser","3":false,"4":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.oPDVbEgyPUdpcvvVRO0UnWxZcC1y298PjKeMLjjteBU","5":"2025-07-07T10:45:31.149Z","6":"magic_link_auth","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:30:31:3031"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:30:31:3031"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:30:31:3031"}
{"email":"<EMAIL>","error":"SQLITE_CONSTRAINT: UNIQUE constraint failed: users.username","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: users.username\n--> in Statement#all([\n  'testuser',\n  '<EMAIL>',\n  'testuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.oPDVbEgyPUdpcvvVRO0UnWxZcC1y298PjKeMLjjteBU',\n  2025-07-07T10:45:31.149Z,\n  'magic_link_auth'\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:125:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:30:31:3031"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"lesavot-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1904:16)\n    at listenInCluster (node:net:1961:12)\n    at Server.listen (node:net:2063:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:368:12)","syscall":"listen","timestamp":"2025-07-07 11:36:57:3657"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:39:58:3958"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:39:58:3958"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:43:04:434"}
{"email":"<EMAIL>","error":"Magic link not found or expired","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:43:04:434"}
{"level":"error","message":"Session token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:50:41:5041"}
{"level":"error","message":"Session token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:54:50:5450"}
{"level":"error","message":"Session token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 11:55:37:5537"}
{"level":"error","message":"Magic link token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 12:01:06:16"}
{"level":"error","message":"Token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 12:01:06:16"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 12:01:06:16"}
{"email":"<EMAIL>","error":"Invalid or expired magic link","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 12:01:06:16"}
{"level":"error","message":"Magic link token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 12:07:39:739"}
{"level":"error","message":"Token verification failed:","service":"lesavot-api","timestamp":"2025-07-07 12:07:39:739"}
{"level":"error","message":"Error verifying magic link:","service":"lesavot-api","timestamp":"2025-07-07 12:07:39:739"}
{"email":"<EMAIL>","error":"Invalid or expired magic link","ip":"::1","level":"error","message":"Magic link verification failed:","service":"lesavot-api","timestamp":"2025-07-07 12:07:39:739"}
{"ip":"::1","level":"error","message":"Error: Record performance metrics not implemented. Database logic required.","method":"POST","path":"/api/v1/metrics/performance","service":"lesavot-api","stack":"Error: Record performance metrics not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\metricsController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at async C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-07 12:48:46:4846","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Record performance metrics not implemented. Database logic required.","method":"POST","path":"/api/v1/metrics/performance","service":"lesavot-api","stack":"Error: Record performance metrics not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\metricsController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at async C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-07 12:49:11:4911","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Record performance metrics not implemented. Database logic required.","method":"POST","path":"/api/v1/metrics/performance","service":"lesavot-api","stack":"Error: Record performance metrics not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\metricsController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at async C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-07 12:53:53:5353","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
