document.addEventListener('DOMContentLoaded', function() {
    // Get reset token from URL
    const urlParams = new URLSearchParams(window.location.search);
    const resetToken = urlParams.get('token') || window.location.pathname.split('/').pop();

    if (!resetToken || resetToken === 'reset-password.html') {
        showNotification('Invalid or missing reset token. Please request a new password reset.', 'error');
        setTimeout(() => {
            window.location.href = 'auth.html';
        }, 3000);
        return;
    }

    // Form elements
    const resetForm = document.getElementById('resetPasswordForm');
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const resetBtn = document.getElementById('resetPasswordBtn');

    // Password toggle functionality
    const toggleNewPassword = document.getElementById('toggleNewPassword');
    const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');

    // Password strength elements
    const strengthSegments = [
        document.getElementById('strength1'),
        document.getElementById('strength2'),
        document.getElementById('strength3'),
        document.getElementById('strength4')
    ];
    const strengthText = document.getElementById('strengthText');

    // Password toggle states
    const passwordToggleStates = {
        newPassword: 0, // 0 = hidden, 1 = visible
        confirmPassword: 0
    };

    // Password toggle functionality
    function togglePasswordVisibility(input, toggleBtn, stateKey) {
        if (passwordToggleStates[stateKey] === 0) {
            input.type = 'text';
            toggleBtn.classList.remove('fa-eye');
            toggleBtn.classList.add('fa-eye-slash');
            toggleBtn.setAttribute('aria-label', 'Hide password');
            passwordToggleStates[stateKey] = 1;
        } else {
            input.type = 'password';
            toggleBtn.classList.remove('fa-eye-slash');
            toggleBtn.classList.add('fa-eye');
            toggleBtn.setAttribute('aria-label', 'Show password');
            passwordToggleStates[stateKey] = 0;
        }
    }

    // Set up password toggles
    toggleNewPassword.addEventListener('click', function() {
        togglePasswordVisibility(newPasswordInput, toggleNewPassword.querySelector('i'), 'newPassword');
    });

    toggleConfirmPassword.addEventListener('click', function() {
        togglePasswordVisibility(confirmPasswordInput, toggleConfirmPassword.querySelector('i'), 'confirmPassword');
    });

    // Password strength calculation
    function calculatePasswordStrength(password) {
        if (!password) return 0;
        let strength = 0;
        if (password.length >= 8) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        return Math.min(strength, 4);
    }

    // Update password strength display
    function updatePasswordStrength(password) {
        const strength = calculatePasswordStrength(password);
        const strengthLevels = ['', 'weak', 'medium', 'good', 'strong'];
        const strengthTexts = ['Password strength', 'Weak', 'Medium', 'Good', 'Strong'];

        // Reset all segments
        strengthSegments.forEach(segment => {
            segment.className = 'strength-segment';
        });

        // Apply strength classes
        for (let i = 0; i < strength; i++) {
            strengthSegments[i].classList.add(strengthLevels[strength]);
        }

        strengthText.textContent = strengthTexts[strength];
    }

    // Password strength monitoring
    newPasswordInput.addEventListener('input', function() {
        updatePasswordStrength(this.value);
    });

    // Form submission
    resetForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        // Validation
        if (!newPassword || !confirmPassword) {
            showNotification('Please fill in all fields', 'error');
            return;
        }

        if (newPassword !== confirmPassword) {
            showNotification('Passwords do not match', 'error');
            return;
        }

        if (newPassword.length < 8) {
            showNotification('Password must be at least 8 characters long', 'error');
            return;
        }

        const strength = calculatePasswordStrength(newPassword);
        if (strength < 2) {
            showNotification('Please choose a stronger password', 'error');
            return;
        }

        // Hash password before sending
        const hashedPassword = await hashPassword(newPassword);

        resetBtn.disabled = true;
        resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting Password...';

        try {
            const apiUrl = window.CONFIG ? window.CONFIG.getApiUrl(`auth/reset-password/${resetToken}`) : `/api/auth/reset-password/${resetToken}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ password: hashedPassword })
            });

            const data = await response.json();

            if (response.ok) {
                showNotification('Password reset successfully! Redirecting to sign in...', 'success');
                setTimeout(() => {
                    window.location.href = 'auth.html';
                }, 2000);
            } else {
                showNotification(data.message || 'Failed to reset password', 'error');
            }
        } catch (error) {
            console.error('Password reset error:', error);
            showNotification('An error occurred. Please try again.', 'error');
        } finally {
            resetBtn.disabled = false;
            resetBtn.innerHTML = '<i class="fas fa-shield-alt"></i> Reset Password';
        }
    });

    // Hash password using SHA-256
    async function hashPassword(password) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hash = await crypto.subtle.digest('SHA-256', data);
        return Array.from(new Uint8Array(hash)).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notificationArea = document.getElementById('notificationArea');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        
        const notificationContent = document.createElement('div');
        notificationContent.className = 'notification-content';
        
        const header = document.createElement('div');
        header.className = 'notification-header';
        header.innerHTML = `<i class="fas fa-${icon}"></i><span class="notification-title">${type.charAt(0).toUpperCase() + type.slice(1)}</span>`;
        
        const messageElement = document.createElement('p');
        messageElement.className = 'notification-message';
        messageElement.textContent = message;
        
        notificationContent.appendChild(header);
        notificationContent.appendChild(messageElement);
        notification.appendChild(notificationContent);
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        notification.appendChild(closeBtn);
        
        notificationArea.appendChild(notification);
        
        closeBtn.addEventListener('click', function() {
            notification.style.opacity = '0';
            setTimeout(() => { notification.remove(); }, 300);
        });
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                setTimeout(() => { if (notification.parentNode) notification.remove(); }, 300);
            }
        }, 5000);
    }
});
