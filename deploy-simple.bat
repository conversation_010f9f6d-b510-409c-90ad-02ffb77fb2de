@echo off
echo ========================================
echo LESAVOT Deployment Script
echo ========================================
echo.
echo This script will deploy:
echo 1. Frontend to: https://github.com/Bechi-cyber/LASAVOT
echo 2. Backend to: https://github.com/Bechi-cyber/LASAVOT-Backend
echo.
pause

echo.
echo ========================================
echo DEPLOYING FRONTEND
echo ========================================
powershell -ExecutionPolicy Bypass -File "deploy-frontend.ps1"

echo.
echo ========================================
echo DEPLOYING BACKEND  
echo ========================================
powershell -ExecutionPolicy Bypass -File "deploy-backend.ps1"

echo.
echo ========================================
echo DEPLOYMENT COMPLETE
echo ========================================
echo.
echo Frontend: https://github.com/Bechi-cyber/LASAVOT
echo Backend:  https://github.com/Bechi-cyber/LASAVOT-Backend
echo.
pause
