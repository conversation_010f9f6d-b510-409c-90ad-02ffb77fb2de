// Debug version of server to identify issues
console.log('🔍 Starting debug server...');

try {
  console.log('📦 Loading dependencies...');
  
  // Load environment variables first
  require('dotenv').config();
  console.log('✅ Environment variables loaded');
  console.log('   NODE_ENV:', process.env.NODE_ENV);
  console.log('   USE_SQLITE:', process.env.USE_SQLITE);
  console.log('   PORT:', process.env.PORT);

  const express = require('express');
  console.log('✅ Express loaded');

  const cors = require('cors');
  console.log('✅ CORS loaded');

  const path = require('path');
  console.log('✅ Path loaded');

  // Try to load database
  console.log('📊 Loading database...');
  const database = require('./utils/database');
  console.log('✅ Database module loaded');

  // Try to load logger
  console.log('📝 Loading logger...');
  const logger = require('./utils/logger');
  console.log('✅ Logger loaded');

  // Try to load auth controller
  console.log('🔐 Loading auth controller...');
  const authController = require('./controllers/authController');
  console.log('✅ Auth controller loaded');

  // Create Express app
  console.log('🚀 Creating Express app...');
  const app = express();
  console.log('✅ Express app created');

  // Basic middleware
  app.use(cors());
  app.use(express.json());
  console.log('✅ Basic middleware configured');

  // Test route
  app.get('/api/test', (req, res) => {
    res.json({ success: true, message: 'Debug server working!' });
  });

  // Simple auth test route
  app.post('/api/auth/test-login', async (req, res) => {
    try {
      console.log('🧪 Test login attempt:', req.body);
      res.json({ success: true, message: 'Test login endpoint working!' });
    } catch (error) {
      console.error('❌ Test login error:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Start server
  const PORT = process.env.PORT || 3000;
  console.log('🔄 Starting server on port', PORT);
  
  app.listen(PORT, () => {
    console.log('✅ Debug server running successfully!');
    console.log(`   URL: http://localhost:${PORT}`);
    console.log(`   Test: http://localhost:${PORT}/api/test`);
    console.log(`   Auth test: http://localhost:${PORT}/api/auth/test-login`);
  });

} catch (error) {
  console.error('❌ Debug server failed to start:', error);
  console.error('❌ Error details:', error.message);
  console.error('❌ Stack trace:', error.stack);
  process.exit(1);
}
