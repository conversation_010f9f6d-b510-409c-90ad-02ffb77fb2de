/**
 * Validation Middleware
 * 
 * This module provides middleware functions for validating request data.
 */

const { body, validationResult } = require('express-validator');
const { ValidationError } = require('../utils/errorHandler');

/**
 * Process validation results and handle errors
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    // Format errors for consistent response
    const formattedErrors = errors.array().map(error => ({
      field: error.param,
      message: error.msg
    }));
    
    return next(new ValidationError('Validation failed', formattedErrors));
  }
  
  next();
};

/**
 * Validate signup request
 */
exports.validateSignup = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  
  body('password')
    .custom((value) => {
      // Check if it's a SHA-256 hash (64 hex characters)
      if (/^[a-f0-9]{64}$/i.test(value)) {
        return true; // Valid SHA-256 hash from frontend
      }
      // Otherwise validate as regular password
      if (value.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }
      if (!/[a-z]/.test(value)) {
        throw new Error('Password must contain at least one lowercase letter');
      }
      if (!/[A-Z]/.test(value)) {
        throw new Error('Password must contain at least one uppercase letter');
      }
      if (!/[0-9]/.test(value)) {
        throw new Error('Password must contain at least one number');
      }
      return true;
    }),
  
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('fullName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),
  
  handleValidationErrors
];

/**
 * Validate login request
 */
exports.validateLogin = [
  body('username')
    .trim()
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3 })
    .withMessage('Username must be at least 3 characters long'),

  body('password')
    .notEmpty()
    .withMessage('Password is required'),

  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('Remember me must be a boolean value'),

  handleValidationErrors
];

/**
 * Validate password update request
 */
exports.validatePasswordUpdate = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/[a-z]/)
    .withMessage('New password must contain at least one lowercase letter')
    .matches(/[A-Z]/)
    .withMessage('New password must contain at least one uppercase letter')
    .matches(/[0-9]/)
    .withMessage('New password must contain at least one number')
    .custom((value, { req }) => {
      if (value === req.body.currentPassword) {
        throw new Error('New password must be different from current password');
      }
      return true;
    }),
  
  body('confirmPassword')
    .notEmpty()
    .withMessage('Please confirm your new password')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
  
  handleValidationErrors
];

/**
 * Validate password reset request
 */
exports.validatePasswordReset = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  
  handleValidationErrors
];

/**
 * Validate new password for reset
 */
exports.validateNewPassword = [
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/)
    .withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/)
    .withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/)
    .withMessage('Password must contain at least one number'),
  
  body('confirmPassword')
    .notEmpty()
    .withMessage('Please confirm your password')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
  
  handleValidationErrors
];

/**
 * Validate profile update request
 */
exports.validateProfileUpdate = [
  body('username')
    .optional()
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('fullName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),
  
  body('bio')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Bio cannot exceed 500 characters'),
  
  handleValidationErrors
];

/**
 * Validate steganography operation request
 */
exports.validateStegoOperation = [
  body('type')
    .isIn(['text', 'image', 'audio'])
    .withMessage('Type must be one of: text, image, audio'),
  
  body('mode')
    .isIn(['encrypt', 'decrypt'])
    .withMessage('Mode must be one of: encrypt, decrypt'),
  
  body('hasPassword')
    .optional()
    .isBoolean()
    .withMessage('hasPassword must be a boolean value'),
  
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata must be an object'),
  
  handleValidationErrors
];

/**
 * Validate MFA challenge request
 */
exports.validateMfaChallenge = [
  body('userId')
    .notEmpty()
    .withMessage('User ID is required'),
  
  body('factorId')
    .notEmpty()
    .withMessage('Factor ID is required'),
  
  handleValidationErrors
];

/**
 * Validate MFA verification request
 */
exports.validateMfaVerify = [
  body('userId')
    .notEmpty()
    .withMessage('User ID is required'),
  
  body('factorId')
    .notEmpty()
    .withMessage('Factor ID is required'),
  
  body('challengeId')
    .notEmpty()
    .withMessage('Challenge ID is required'),
  
  body('code')
    .notEmpty()
    .withMessage('Verification code is required')
    .isLength({ min: 6, max: 6 })
    .withMessage('Verification code must be 6 digits')
    .isNumeric()
    .withMessage('Verification code must contain only numbers'),
  
  handleValidationErrors
];
