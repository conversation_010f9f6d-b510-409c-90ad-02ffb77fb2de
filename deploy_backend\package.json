{"name": "lesavot-api", "version": "1.0.0", "description": "Backend API for LESAVOT Steganography Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:https": "nodemon server.js", "generate-cert": "node scripts/generate-ssl-cert.js", "lint": "eslint .", "test": "jest", "migrate": "node scripts/migrate-db.js", "backup-db": "node scripts/backup-db.js", "monitor": "node scripts/monitor.js", "build": "echo 'No build step required for Node.js backend'"}, "dependencies": {"bcrypt": "^6.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "morgan": "^1.10.0", "node-cron": "^3.0.2", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "pg": "^8.16.3", "pg-pool": "^3.10.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}