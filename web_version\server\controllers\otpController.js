const otpService = require('../services/otpService');
const OTPUser = require('../models/OTPUser');
const logger = require('../utils/logger');
const rateLimit = require('express-rate-limit');

// Rate limiting for OTP requests
const otpRequestLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // 3 OTP requests per window
    message: {
        status: 'error',
        message: 'Too many OTP requests. Please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Rate limiting for OTP verification
const otpVerifyLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 verification attempts per window
    message: {
        status: 'error',
        message: 'Too many verification attempts. Please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

class OTPController {
    /**
     * Request OTP for signup
     */
    static async requestSignupOTP(req, res) {
        try {
            const { email } = req.body;

            // Validate email
            if (!email || !email.includes('@')) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Valid email is required'
                });
            }

            // Check if user already exists
            const existingUser = await OTPUser.findByEmail(email);
            if (existingUser && existingUser.isVerified) {
                return res.status(400).json({
                    status: 'error',
                    message: 'User already exists. Please sign in instead.'
                });
            }

            // Send OTP
            const result = await otpService.sendOTP(email, 'signup');

            logger.info(`Signup OTP requested for: ${email}`);

            res.status(200).json({
                status: 'success',
                message: 'OTP sent to your email address',
                expiresAt: result.expiresAt
            });

        } catch (error) {
            logger.error('Error requesting signup OTP:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Failed to send OTP. Please try again.'
            });
        }
    }

    /**
     * Request OTP for signin
     */
    static async requestSigninOTP(req, res) {
        try {
            const { email } = req.body;

            // Validate email
            if (!email || !email.includes('@')) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Valid email is required'
                });
            }

            // Check if user exists
            const user = await OTPUser.findByEmail(email);
            if (!user) {
                return res.status(404).json({
                    status: 'error',
                    message: 'User not found. Please sign up first.'
                });
            }

            // Check if account is locked
            if (user.isAccountLocked()) {
                return res.status(423).json({
                    status: 'error',
                    message: 'Account is temporarily locked due to failed attempts. Please try again later.'
                });
            }

            // Send OTP
            const result = await otpService.sendOTP(email, 'signin');

            logger.info(`Signin OTP requested for: ${email}`);

            res.status(200).json({
                status: 'success',
                message: 'OTP sent to your email address',
                expiresAt: result.expiresAt
            });

        } catch (error) {
            logger.error('Error requesting signin OTP:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Failed to send OTP. Please try again.'
            });
        }
    }

    /**
     * Verify OTP and complete authentication
     */
    static async verifyOTP(req, res) {
        try {
            const { email, otp } = req.body;

            // Validate input
            if (!email || !otp) {
                return res.status(400).json({
                    status: 'error',
                    message: 'Email and OTP are required'
                });
            }

            // Verify OTP
            const verificationResult = await otpService.verifyOTP(email, otp);

            if (!verificationResult.success) {
                return res.status(400).json({
                    status: 'error',
                    message: verificationResult.message
                });
            }

            let user;
            const isSignup = verificationResult.type === 'signup';

            if (isSignup) {
                // Create new user for signup
                try {
                    user = await OTPUser.create(email);
                    await user.markAsVerified();
                    logger.info(`New user created and verified: ${email}`);
                } catch (error) {
                    if (error.message.includes('already exists')) {
                        // User exists, treat as signin
                        user = await OTPUser.findByEmail(email);
                        if (user) {
                            await user.markAsVerified();
                            await user.resetFailedAttempts();
                        }
                    } else {
                        throw error;
                    }
                }
            } else {
                // Signin - find existing user
                user = await OTPUser.findByEmail(email);
                if (!user) {
                    return res.status(404).json({
                        status: 'error',
                        message: 'User not found'
                    });
                }

                // Update user
                await user.updateLastLogin();
                await user.resetFailedAttempts();
            }

            // Generate session token
            const sessionToken = otpService.generateSessionToken(user);

            logger.info(`OTP verification successful for: ${email} (${verificationResult.type})`);

            res.status(200).json({
                status: 'success',
                message: `${isSignup ? 'Signup' : 'Signin'} successful`,
                user: user.getProfile(),
                sessionToken,
                type: verificationResult.type
            });

        } catch (error) {
            logger.error('Error verifying OTP:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Verification failed. Please try again.'
            });
        }
    }

    /**
     * Get current user profile
     */
    static async getProfile(req, res) {
        try {
            const user = await OTPUser.findById(req.user.userId);
            
            if (!user) {
                return res.status(404).json({
                    status: 'error',
                    message: 'User not found'
                });
            }

            res.status(200).json({
                status: 'success',
                user: user.getProfile()
            });

        } catch (error) {
            logger.error('Error getting profile:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Failed to get profile'
            });
        }
    }

    /**
     * Update user profile
     */
    static async updateProfile(req, res) {
        try {
            const { displayName } = req.body;
            
            const user = await OTPUser.findById(req.user.userId);
            
            if (!user) {
                return res.status(404).json({
                    status: 'error',
                    message: 'User not found'
                });
            }

            const updatedProfile = await user.updateProfile({ displayName });

            res.status(200).json({
                status: 'success',
                message: 'Profile updated successfully',
                user: updatedProfile
            });

        } catch (error) {
            logger.error('Error updating profile:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Failed to update profile'
            });
        }
    }

    /**
     * Check authentication status
     */
    static async getStatus(req, res) {
        try {
            const user = await OTPUser.findById(req.user.userId);
            
            if (!user) {
                return res.status(401).json({
                    status: 'error',
                    message: 'User not found',
                    isAuthenticated: false
                });
            }

            res.status(200).json({
                status: 'success',
                isAuthenticated: true,
                user: user.getProfile()
            });

        } catch (error) {
            logger.error('Error checking status:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Failed to check status',
                isAuthenticated: false
            });
        }
    }

    /**
     * Logout user
     */
    static async logout(req, res) {
        try {
            // In a more sophisticated setup, you might invalidate the token
            // For now, we'll just return success and let the client handle token removal
            
            logger.info(`User logged out: ${req.user.email}`);

            res.status(200).json({
                status: 'success',
                message: 'Logged out successfully'
            });

        } catch (error) {
            logger.error('Error during logout:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Logout failed'
            });
        }
    }

    /**
     * Get OTP service statistics (admin only)
     */
    static async getOTPStats(req, res) {
        try {
            const stats = otpService.getStats();
            
            res.status(200).json({
                status: 'success',
                stats
            });

        } catch (error) {
            logger.error('Error getting OTP stats:', error.message);
            res.status(500).json({
                status: 'error',
                message: 'Failed to get statistics'
            });
        }
    }
}

// Export controller with rate limiting middleware
module.exports = {
    requestSignupOTP: [otpRequestLimiter, OTPController.requestSignupOTP],
    requestSigninOTP: [otpRequestLimiter, OTPController.requestSigninOTP],
    verifyOTP: [otpVerifyLimiter, OTPController.verifyOTP],
    getProfile: OTPController.getProfile,
    updateProfile: OTPController.updateProfile,
    getStatus: OTPController.getStatus,
    logout: OTPController.logout,
    getOTPStats: OTPController.getOTPStats
};
