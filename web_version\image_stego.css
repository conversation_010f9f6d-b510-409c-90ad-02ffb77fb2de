/* LESAVOT - Image Steganography
   Additional styles specific to image steganography */

/* File upload styling */
.file-upload-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.file-input {
    position: absolute;
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    z-index: -1;
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    padding: 0.625rem 1rem;
    background-color: var(--primary-blue);
    color: var(--text-white);
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    font-size: 0.875rem;
}

.file-upload-label i {
    margin-right: 0.5rem;
}

.file-upload-label:hover {
    background-color: var(--primary-hover);
}

.file-name {
    color: var(--text-medium);
    font-size: 0.875rem;
}

/* Image preview styling */
.image-preview-container {
    margin-top: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    max-width: 100%;
    background-color: var(--lightest-blue);
    text-align: center;
    padding: 0.5rem;
}

.image-preview-container img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
}

/* Image result styling */
.image-result-container {
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    max-width: 100%;
    background-color: var(--lightest-blue);
    text-align: center;
    padding: 0.5rem;
}

.image-result-container img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .file-upload-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .file-name {
        margin-top: 0.5rem;
    }
}
