<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LESAVOT | User Profile</title>
    <link rel="stylesheet" href="text_stego.css">
    <link rel="stylesheet" href="cybersecurity_elements.css">
    <link rel="stylesheet" href="snowy_raindrops.css">
    <link rel="stylesheet" href="profile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <header>
            <div class="header-container">
                <!-- Floating snowflakes -->
                <div class="snowflake-container">
                    <!-- Single raindrops -->
                    <div class="snowflake snow-1">│</div>
                    <div class="snowflake snow-2">│</div>
                    <div class="snowflake snow-3">│</div>
                    <div class="snowflake snow-4">│</div>
                    <div class="snowflake snow-5">│</div>
                    <div class="snowflake snow-6">│</div>
                    <div class="snowflake snow-7">│</div>
                    <div class="snowflake snow-8">│</div>
                    <div class="snowflake snow-9">│</div>
                    <div class="snowflake snow-10">│</div>
                    <div class="snowflake snow-11">│</div>
                    <div class="snowflake snow-12">│</div>
                    <div class="snowflake snow-13">│</div>
                    <div class="snowflake snow-14">│</div>
                    <div class="snowflake snow-15">│</div>
                    <div class="snowflake snow-16">│</div>
                    <div class="snowflake snow-17">│</div>
                    <div class="snowflake snow-18">│</div>
                    <div class="snowflake snow-19">│</div>
                    <div class="snowflake snow-20">│</div>

                    <!-- Raindrop lines -->
                    <div class="raindrop-line rain-line-1">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-2">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-3">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-4">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-5">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-6">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-7">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-8">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-9">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-10">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-11">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-12">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-13">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-14">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-15">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-16">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-17">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-18">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-19">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-20">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-21">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-22">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-23">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-24">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-25">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-26">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-27">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-28">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-29">│<br>│<br>│</div>
                    <div class="raindrop-line rain-line-30">│<br>│<br>│</div>
                </div>

                <div class="logo">
                    <i class="fas fa-shield-alt logo-icon"></i>
                    <span class="logo-text">LESAVOT</span>
                </div>
                <div class="subtitle">THE MORE YOU LOOK, THE LESS YOU SEE</div>
            </div>
            <div class="user-info" id="userInfo">
                <span id="welcomeMessage">Welcome</span>
                <div class="user-actions">
                    <button type="button" class="btn-icon active" title="Profile">
                        <i class="fas fa-user"></i>
                    </button>
                    <a href="auth.html" class="btn-icon" title="Sign Out" id="signOutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </header>

        <div class="tab-navigation">
            <button type="button" class="tab-btn" onclick="window.location.href='text_stego.html'">
                <i class="fas fa-font"></i>
                <span>Text</span>
            </button>
            <button type="button" class="tab-btn" onclick="window.location.href='image_stego.html'">
                <i class="fas fa-image"></i>
                <span>Image</span>
            </button>
            <button type="button" class="tab-btn" onclick="window.location.href='audio_stego.html'">
                <i class="fas fa-volume-up"></i>
                <span>Audio</span>
            </button>
            <button type="button" class="tab-btn" onclick="window.location.href='history.html'">
                <i class="fas fa-history"></i>
                <span>History</span>
            </button>
        </div>

        <div id="notificationArea"></div>

        <main>
            <!-- Main Content Area -->
            <div class="content-container">
                <div class="profile-grid">
                    <!-- Profile Information Card -->
                    <div class="card profile-card">
                        <div class="card-header">
                            <i class="fas fa-user-shield"></i>
                            <h2>Profile Information</h2>
                        </div>
                        <div class="card-body">
                            <div class="profile-avatar">
                                <i class="fas fa-user-circle"></i>
                                <div class="profile-name" id="profileName">Loading...</div>
                                <div class="profile-email" id="profileEmail">Loading...</div>
                            </div>
                            <div class="form-group">
                                <label for="fullName">Full Name</label>
                                <input type="text" id="fullName" placeholder="Your full name">
                            </div>
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" placeholder="Your username">
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" placeholder="Your email address" readonly>
                                <small class="helper-text">Email address cannot be changed.</small>
                            </div>
                            <button type="button" id="updateProfileBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                    </div>

                    <!-- Security Settings Card -->
                    <div class="card security-card">
                        <div class="card-header">
                            <i class="fas fa-lock"></i>
                            <h2>Security Settings</h2>
                        </div>
                        <div class="card-body">
                            <div class="security-section">
                                <h3>Change Password</h3>
                                <div class="form-group">
                                    <label for="currentPassword">Current Password</label>
                                    <input type="password" id="currentPassword" placeholder="Your current password">
                                </div>
                                <div class="form-group">
                                    <label for="newPassword">New Password</label>
                                    <input type="password" id="newPassword" placeholder="Your new password">
                                </div>
                                <div class="form-group">
                                    <label for="confirmNewPassword">Confirm New Password</label>
                                    <input type="password" id="confirmNewPassword" placeholder="Confirm your new password">
                                </div>
                                <div class="password-strength">
                                    <div class="strength-meter">
                                        <div class="strength-segment"></div>
                                        <div class="strength-segment"></div>
                                        <div class="strength-segment"></div>
                                        <div class="strength-segment"></div>
                                    </div>
                                    <span class="strength-text">Password strength</span>
                                </div>
                                <button type="button" id="changePasswordBtn" class="btn btn-primary">
                                    <i class="fas fa-key"></i> Change Password
                                </button>
                            </div>

                            <div class="security-section">
                                <h3>Multi-Factor Authentication</h3>
                                <div class="mfa-status">
                                    <div class="status-indicator" id="mfaStatus">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        <span>Checking MFA status...</span>
                                    </div>
                                </div>
                                <div class="mfa-actions">
                                    <button type="button" id="setupMfaBtn" class="btn btn-outline">
                                        <i class="fas fa-shield-alt"></i> Setup MFA
                                    </button>
                                    <button type="button" id="disableMfaBtn" class="btn btn-outline btn-danger" style="display: none;">
                                        <i class="fas fa-times-circle"></i> Disable MFA
                                    </button>
                                </div>
                            </div>

                            <div class="security-section danger-zone">
                                <h3>Danger Zone</h3>
                                <p>Permanently delete your account and all associated data.</p>
                                <button type="button" id="deleteAccountBtn" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="api-client.js"></script>
    <script src="user-auth.js"></script>
    <script src="profile.js"></script>
</body>
</html>
